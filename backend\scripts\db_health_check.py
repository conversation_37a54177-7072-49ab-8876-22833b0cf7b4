#!/usr/bin/env python3
"""
CampusGuard 数据库健康检查脚本
用于监控数据库状态、性能和数据完整性
"""

import sys
import os
import asyncio
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any
import json

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from sqlalchemy import text, inspect
from sqlalchemy.exc import SQLAlchemyError
from loguru import logger

from app.core.database import engine, SessionLocal
from app.core.config import settings
from app.models import Device, Alert, PerformanceMetric, Conversation


class DatabaseHealthChecker:
    """数据库健康检查器"""
    
    def __init__(self):
        self.results = {
            "timestamp": datetime.now().isoformat(),
            "overall_status": "UNKNOWN",
            "checks": {},
            "recommendations": [],
            "alerts": []
        }
    
    async def run_all_checks(self) -> Dict[str, Any]:
        """运行所有健康检查"""
        logger.info("开始数据库健康检查...")
        
        checks = [
            ("connectivity", self.check_connectivity),
            ("table_status", self.check_table_status),
            ("data_integrity", self.check_data_integrity),
            ("performance", self.check_performance),
            ("storage", self.check_storage),
            ("backup_status", self.check_backup_status),
            ("migration_status", self.check_migration_status)
        ]
        
        passed_checks = 0
        total_checks = len(checks)
        
        for check_name, check_func in checks:
            try:
                logger.info(f"执行检查: {check_name}")
                result = await check_func()
                self.results["checks"][check_name] = result
                
                if result["status"] == "PASS":
                    passed_checks += 1
                elif result["status"] == "FAIL":
                    self.results["alerts"].append(f"{check_name}: {result['message']}")
                
            except Exception as e:
                logger.error(f"检查 {check_name} 失败: {e}")
                self.results["checks"][check_name] = {
                    "status": "ERROR",
                    "message": str(e),
                    "timestamp": datetime.now().isoformat()
                }
                self.results["alerts"].append(f"{check_name}: 检查执行失败")
        
        # 计算总体状态
        if passed_checks == total_checks:
            self.results["overall_status"] = "HEALTHY"
        elif passed_checks >= total_checks * 0.8:
            self.results["overall_status"] = "WARNING"
        else:
            self.results["overall_status"] = "CRITICAL"
        
        logger.info(f"健康检查完成，总体状态: {self.results['overall_status']}")
        return self.results
    
    async def check_connectivity(self) -> Dict[str, Any]:
        """检查数据库连接"""
        try:
            with engine.connect() as conn:
                result = conn.execute(text("SELECT 1"))
                result.fetchone()
            
            return {
                "status": "PASS",
                "message": "数据库连接正常",
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            return {
                "status": "FAIL",
                "message": f"数据库连接失败: {e}",
                "timestamp": datetime.now().isoformat()
            }
    
    async def check_table_status(self) -> Dict[str, Any]:
        """检查表状态"""
        try:
            inspector = inspect(engine)
            tables = inspector.get_table_names()
            
            expected_tables = [
                'devices', 'alerts', 'performance_metrics', 
                'conversations', 'messages', 'threat_intelligence',
                'alembic_version'
            ]
            
            missing_tables = [t for t in expected_tables if t not in tables]
            
            if missing_tables:
                return {
                    "status": "FAIL",
                    "message": f"缺少表: {', '.join(missing_tables)}",
                    "details": {"missing_tables": missing_tables, "existing_tables": tables},
                    "timestamp": datetime.now().isoformat()
                }
            
            return {
                "status": "PASS",
                "message": f"所有必需的表都存在 ({len(tables)} 个表)",
                "details": {"table_count": len(tables), "tables": tables},
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "status": "ERROR",
                "message": f"检查表状态失败: {e}",
                "timestamp": datetime.now().isoformat()
            }
    
    async def check_data_integrity(self) -> Dict[str, Any]:
        """检查数据完整性"""
        try:
            with SessionLocal() as db:
                # 检查基础数据统计
                device_count = db.query(Device).count()
                alert_count = db.query(Alert).count()
                metric_count = db.query(PerformanceMetric).count()
                conversation_count = db.query(Conversation).count()
                
                # 检查数据一致性
                issues = []
                
                # 检查孤立的告警（没有对应设备的告警）
                orphaned_alerts = db.execute(text("""
                    SELECT COUNT(*) FROM alerts a 
                    LEFT JOIN devices d ON a.device_id = d.id 
                    WHERE a.device_id IS NOT NULL AND d.id IS NULL
                """)).scalar()
                
                if orphaned_alerts > 0:
                    issues.append(f"发现 {orphaned_alerts} 个孤立告警")
                
                # 检查孤立的性能指标
                orphaned_metrics = db.execute(text("""
                    SELECT COUNT(*) FROM performance_metrics pm 
                    LEFT JOIN devices d ON pm.device_id = d.id 
                    WHERE d.id IS NULL
                """)).scalar()
                
                if orphaned_metrics > 0:
                    issues.append(f"发现 {orphaned_metrics} 个孤立性能指标")
                
                # 检查重复设备IP
                duplicate_ips = db.execute(text("""
                    SELECT ip_address, COUNT(*) as count 
                    FROM devices 
                    WHERE is_active = 1 
                    GROUP BY ip_address 
                    HAVING count > 1
                """)).fetchall()
                
                if duplicate_ips:
                    issues.append(f"发现 {len(duplicate_ips)} 个重复IP地址")
                
                status = "FAIL" if issues else "PASS"
                message = "; ".join(issues) if issues else "数据完整性检查通过"
                
                return {
                    "status": status,
                    "message": message,
                    "details": {
                        "device_count": device_count,
                        "alert_count": alert_count,
                        "metric_count": metric_count,
                        "conversation_count": conversation_count,
                        "orphaned_alerts": orphaned_alerts,
                        "orphaned_metrics": orphaned_metrics,
                        "duplicate_ips": len(duplicate_ips)
                    },
                    "timestamp": datetime.now().isoformat()
                }
                
        except Exception as e:
            return {
                "status": "ERROR",
                "message": f"数据完整性检查失败: {e}",
                "timestamp": datetime.now().isoformat()
            }
    
    async def check_performance(self) -> Dict[str, Any]:
        """检查数据库性能"""
        try:
            with engine.connect() as conn:
                # 检查连接数
                connections = conn.execute(text("SHOW PROCESSLIST")).fetchall()
                connection_count = len(connections)
                
                # 检查表大小
                table_sizes = conn.execute(text("""
                    SELECT table_name, 
                           ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb
                    FROM information_schema.TABLES 
                    WHERE table_schema = %s
                    ORDER BY (data_length + index_length) DESC
                """), (settings.DATABASE_NAME,)).fetchall()
                
                # 检查慢查询
                slow_queries = conn.execute(text("""
                    SHOW VARIABLES LIKE 'slow_query_log'
                """)).fetchone()
                
                # 性能警告
                warnings = []
                if connection_count > 50:
                    warnings.append(f"连接数过多: {connection_count}")
                
                large_tables = [t for t in table_sizes if t[1] > 1000]  # 大于1GB
                if large_tables:
                    warnings.append(f"发现大表: {len(large_tables)} 个")
                
                status = "WARNING" if warnings else "PASS"
                message = "; ".join(warnings) if warnings else "性能检查正常"
                
                return {
                    "status": status,
                    "message": message,
                    "details": {
                        "connection_count": connection_count,
                        "table_sizes": [{"table": t[0], "size_mb": t[1]} for t in table_sizes[:5]],
                        "slow_query_log_enabled": slow_queries[1] if slow_queries else "unknown"
                    },
                    "timestamp": datetime.now().isoformat()
                }
                
        except Exception as e:
            return {
                "status": "ERROR",
                "message": f"性能检查失败: {e}",
                "timestamp": datetime.now().isoformat()
            }
    
    async def check_storage(self) -> Dict[str, Any]:
        """检查存储空间"""
        try:
            with engine.connect() as conn:
                # 检查数据库大小
                db_size = conn.execute(text("""
                    SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
                    FROM information_schema.TABLES 
                    WHERE table_schema = %s
                """), (settings.DATABASE_NAME,)).scalar()
                
                # 检查磁盘空间（如果可能）
                disk_info = None
                try:
                    disk_usage = conn.execute(text("""
                        SELECT @@datadir as datadir
                    """)).scalar()
                    
                    # 这里可以添加更多磁盘空间检查逻辑
                    
                except:
                    pass
                
                warnings = []
                if db_size > 10000:  # 大于10GB
                    warnings.append(f"数据库大小较大: {db_size} MB")
                
                status = "WARNING" if warnings else "PASS"
                message = "; ".join(warnings) if warnings else f"存储检查正常，数据库大小: {db_size} MB"
                
                return {
                    "status": status,
                    "message": message,
                    "details": {
                        "database_size_mb": db_size,
                        "disk_info": disk_info
                    },
                    "timestamp": datetime.now().isoformat()
                }
                
        except Exception as e:
            return {
                "status": "ERROR",
                "message": f"存储检查失败: {e}",
                "timestamp": datetime.now().isoformat()
            }
    
    async def check_backup_status(self) -> Dict[str, Any]:
        """检查备份状态"""
        try:
            backup_dir = Path("/var/backups/campusguard")
            
            if not backup_dir.exists():
                return {
                    "status": "WARNING",
                    "message": "备份目录不存在",
                    "timestamp": datetime.now().isoformat()
                }
            
            # 查找最近的备份文件
            backup_files = list(backup_dir.glob("campusguard_*.sql*"))
            
            if not backup_files:
                return {
                    "status": "WARNING",
                    "message": "未找到备份文件",
                    "timestamp": datetime.now().isoformat()
                }
            
            # 获取最新备份文件
            latest_backup = max(backup_files, key=lambda x: x.stat().st_mtime)
            backup_age = datetime.now() - datetime.fromtimestamp(latest_backup.stat().st_mtime)
            
            warnings = []
            if backup_age.days > 1:
                warnings.append(f"最新备份已过期 {backup_age.days} 天")
            
            status = "WARNING" if warnings else "PASS"
            message = "; ".join(warnings) if warnings else f"备份状态正常，最新备份: {backup_age.seconds // 3600} 小时前"
            
            return {
                "status": status,
                "message": message,
                "details": {
                    "backup_count": len(backup_files),
                    "latest_backup": str(latest_backup),
                    "backup_age_hours": backup_age.total_seconds() // 3600
                },
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "status": "ERROR",
                "message": f"备份状态检查失败: {e}",
                "timestamp": datetime.now().isoformat()
            }
    
    async def check_migration_status(self) -> Dict[str, Any]:
        """检查迁移状态"""
        try:
            with engine.connect() as conn:
                # 检查alembic_version表
                try:
                    current_revision = conn.execute(text(
                        "SELECT version_num FROM alembic_version"
                    )).scalar()
                    
                    if not current_revision:
                        return {
                            "status": "WARNING",
                            "message": "未找到当前迁移版本",
                            "timestamp": datetime.now().isoformat()
                        }
                    
                    # 检查是否有待应用的迁移
                    versions_dir = Path("alembic/versions")
                    if versions_dir.exists():
                        migration_files = list(versions_dir.glob("*.py"))
                        migration_count = len(migration_files)
                    else:
                        migration_count = 0
                    
                    return {
                        "status": "PASS",
                        "message": f"迁移状态正常，当前版本: {current_revision[:8]}",
                        "details": {
                            "current_revision": current_revision,
                            "migration_files_count": migration_count
                        },
                        "timestamp": datetime.now().isoformat()
                    }
                    
                except SQLAlchemyError:
                    return {
                        "status": "WARNING",
                        "message": "alembic_version表不存在，可能未初始化迁移",
                        "timestamp": datetime.now().isoformat()
                    }
                    
        except Exception as e:
            return {
                "status": "ERROR",
                "message": f"迁移状态检查失败: {e}",
                "timestamp": datetime.now().isoformat()
            }
    
    def generate_recommendations(self):
        """生成改进建议"""
        recommendations = []
        
        for check_name, check_result in self.results["checks"].items():
            if check_result["status"] == "FAIL":
                if check_name == "connectivity":
                    recommendations.append("检查数据库服务状态和网络连接")
                elif check_name == "table_status":
                    recommendations.append("运行数据库迁移以创建缺失的表")
                elif check_name == "data_integrity":
                    recommendations.append("清理孤立数据和重复记录")
                elif check_name == "performance":
                    recommendations.append("优化数据库查询和索引")
                elif check_name == "storage":
                    recommendations.append("清理旧数据或扩展存储空间")
                elif check_name == "backup_status":
                    recommendations.append("设置自动备份任务")
                elif check_name == "migration_status":
                    recommendations.append("初始化或更新数据库迁移")
        
        self.results["recommendations"] = recommendations


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="CampusGuard 数据库健康检查")
    parser.add_argument("--output", "-o", help="输出文件路径")
    parser.add_argument("--format", "-f", choices=["json", "text"], default="text", help="输出格式")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")
    
    args = parser.parse_args()
    
    # 配置日志
    logger.remove()
    if args.verbose:
        logger.add(sys.stdout, level="DEBUG")
    else:
        logger.add(sys.stdout, level="INFO")
    
    # 执行健康检查
    checker = DatabaseHealthChecker()
    results = await checker.run_all_checks()
    checker.generate_recommendations()
    
    # 输出结果
    if args.format == "json":
        output = json.dumps(results, indent=2, ensure_ascii=False)
    else:
        output = format_text_output(results)
    
    if args.output:
        with open(args.output, "w", encoding="utf-8") as f:
            f.write(output)
        logger.info(f"结果已保存到: {args.output}")
    else:
        print(output)
    
    # 设置退出码
    if results["overall_status"] == "CRITICAL":
        sys.exit(2)
    elif results["overall_status"] == "WARNING":
        sys.exit(1)
    else:
        sys.exit(0)


def format_text_output(results: Dict[str, Any]) -> str:
    """格式化文本输出"""
    output = []
    
    # 标题
    output.append("=" * 60)
    output.append("CampusGuard 数据库健康检查报告")
    output.append("=" * 60)
    output.append(f"检查时间: {results['timestamp']}")
    output.append(f"总体状态: {results['overall_status']}")
    output.append("")
    
    # 检查结果
    output.append("检查结果:")
    output.append("-" * 40)
    for check_name, check_result in results["checks"].items():
        status_icon = {
            "PASS": "✅",
            "WARNING": "⚠️",
            "FAIL": "❌",
            "ERROR": "💥"
        }.get(check_result["status"], "❓")
        
        output.append(f"{status_icon} {check_name}: {check_result['message']}")
    
    output.append("")
    
    # 告警
    if results["alerts"]:
        output.append("⚠️ 告警:")
        output.append("-" * 40)
        for alert in results["alerts"]:
            output.append(f"• {alert}")
        output.append("")
    
    # 建议
    if results["recommendations"]:
        output.append("💡 改进建议:")
        output.append("-" * 40)
        for rec in results["recommendations"]:
            output.append(f"• {rec}")
        output.append("")
    
    output.append("=" * 60)
    
    return "\n".join(output)


if __name__ == "__main__":
    asyncio.run(main())