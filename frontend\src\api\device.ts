import request from './request'
import type { Devi<PERSON>, DeviceStats, DeviceType, DeviceStatus } from '@/types/device'

export interface DeviceListParams {
  skip?: number
  limit?: number
  device_type?: DeviceType
  status?: DeviceStatus
}

export interface DeviceCreateData {
  name: string
  ip_address: string
  mac_address?: string
  device_type: DeviceType
  vendor?: string
  model?: string
  description?: string
  location?: string
  building?: string
  floor?: string
  room?: string
  subnet?: string
  vlan_id?: number
  snmp_community?: string
  snmp_version?: string
  snmp_port?: number
}

export interface DeviceUpdateData extends Partial<DeviceCreateData> {
  status?: DeviceStatus
  cpu_usage?: number
  memory_usage?: number
  disk_usage?: number
}

export const deviceApi = {
  // Get device list
  getDevices(params?: DeviceListParams) {
    return request.get<Device[]>('/devices', { params })
  },

  // Get device by ID
  getDevice(id: number) {
    return request.get<Device>(`/devices/${id}`)
  },

  // Create device
  createDevice(data: DeviceCreateData) {
    return request.post<Device>('/devices', data)
  },

  // Update device
  updateDevice(id: number, data: DeviceUpdateData) {
    return request.put<Device>(`/devices/${id}`, data)
  },

  // Delete device
  deleteDevice(id: number) {
    return request.delete(`/devices/${id}`)
  },

  // Get device statistics
  getStats() {
    return request.get<DeviceStats>('/devices/stats')
  },

  // Test device connection
  testConnection(id: number) {
    return request.post(`/devices/${id}/test-connection`)
  },

  // Collect device metrics
  collectMetrics(id: number) {
    return request.post(`/devices/${id}/collect-metrics`)
  },

  // Get device health
  getHealth(id: number) {
    return request.get(`/devices/${id}/health`)
  }
}
