# CampusGuard技术架构

## 八层架构设计
1. **数据采集层**：SNMP采集器 + python3-nmap网络扫描
2. **数据处理层**：pandas数据清洗 + 规则引擎异常检测
3. **智能分析层**：OpenAI Agents + DeepSeek V3 AI分析
4. **业务逻辑层**：FastAPI业务处理 + SQLAlchemy ORM
5. **数据存储层**：MySQL 8.0数据库
6. **API接口层**：RESTful API + WebSocket实时通信
7. **前端展示层**：Vue.js + Ant Design Vue UI
8. **用户交互层**：Web界面 + AI对话界面

## 简化原则
- **移除认证系统**：删除JWT Token和RBAC权限控制
- **移除缓存层**：删除Redis缓存和复杂缓存策略
- **移除消息队列**：删除Celery任务队列
- **移除TypeScript**：使用纯JavaScript降低开发门槛
- **简化Agent管理**：从200+行复杂工厂类简化为30行基础管理器

## 核心组件
- **DeepSeek API配置**：https://api.deepseek.com/v1
- **WebSocket管理**：自动重连机制 + 心跳检测
- **AI对话组件**：ant-design-x-vue Conversations组件
- **网络拓扑图**：@antv/g6 可视化引擎