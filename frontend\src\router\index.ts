import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

// Layout
import Layout from '@/layout/index.vue'

// Views
const Dashboard = () => import('@/views/dashboard/index.vue')
const DeviceList = () => import('@/views/devices/index.vue')
const DeviceDetail = () => import('@/views/devices/detail.vue')
const AlertList = () => import('@/views/alerts/index.vue')
const Performance = () => import('@/views/performance/index.vue')
const Topology = () => import('@/views/topology/index.vue')
const AIChat = () => import('@/views/ai/chat.vue')
const AIAnalysis = () => import('@/views/ai/analysis.vue')

// Security Views
const SecurityOverview = () => import('@/views/security/index.vue')
const ThreatIntel = () => import('@/views/security/threat-intel.vue')
const IPReputation = () => import('@/views/security/ip-reputation.vue')
const Blacklist = () => import('@/views/security/blacklist.vue')

// Monitoring Views
const MonitoringControl = () => import('@/views/monitoring/control.vue')
const DeviceDiscovery = () => import('@/views/monitoring/discovery.vue')

const Settings = () => import('@/views/settings/index.vue')

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/',
    component: Layout,
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: Dashboard,
        meta: {
          title: '监控大屏',
          icon: 'dashboard',
          keepAlive: true
        }
      },
      {
        path: 'devices',
        name: 'Devices',
        component: DeviceList,
        meta: {
          title: '设备管理',
          icon: 'desktop',
          keepAlive: true
        }
      },
      {
        path: 'devices/:id',
        name: 'DeviceDetail',
        component: DeviceDetail,
        meta: {
          title: '设备详情',
          hidden: true,
          activeMenu: '/devices'
        }
      },
      {
        path: 'alerts',
        name: 'Alerts',
        component: AlertList,
        meta: {
          title: '告警管理',
          icon: 'alert',
          keepAlive: true
        }
      },
      {
        path: 'performance',
        name: 'Performance',
        component: Performance,
        meta: {
          title: '性能监控',
          icon: 'line-chart',
          keepAlive: true
        }
      },
      {
        path: 'topology',
        name: 'Topology',
        component: Topology,
        meta: {
          title: '网络拓扑',
          icon: 'cluster',
          keepAlive: true
        }
      },
      {
        path: 'ai',
        name: 'AI',
        meta: {
          title: 'AI助手',
          icon: 'robot'
        },
        children: [
          {
            path: 'chat',
            name: 'AIChat',
            component: AIChat,
            meta: {
              title: 'AI对话',
              icon: 'message',
              keepAlive: true
            }
          },
          {
            path: 'analysis',
            name: 'AIAnalysis',
            component: AIAnalysis,
            meta: {
              title: 'AI分析',
              icon: 'experiment',
              keepAlive: true
            }
          },
          {
            path: 'analysis-history',
            name: 'AIAnalysisHistory',
            component: () => import('@/views/ai/analysis-history.vue'),
            meta: {
              title: 'AI分析历史',
              icon: 'history',
              keepAlive: true
            }
          }
        ]
      },
      {
        path: 'security',
        name: 'Security',
        meta: {
          title: '网络安全',
          icon: 'safety-certificate'
        },
        children: [
          {
            path: '',
            name: 'SecurityOverview',
            component: SecurityOverview,
            meta: {
              title: '安全总览',
              icon: 'security-scan',
              keepAlive: true
            }
          },
          {
            path: 'threat-intel',
            name: 'ThreatIntel',
            component: ThreatIntel,
            meta: {
              title: '威胁情报',
              icon: 'security-scan',
              keepAlive: true
            }
          },
          {
            path: 'ip-reputation',
            name: 'IPReputation',
            component: IPReputation,
            meta: {
              title: 'IP信誉查询',
              icon: 'search',
              keepAlive: true
            }
          },
          {
            path: 'blacklist',
            name: 'Blacklist',
            component: Blacklist,
            meta: {
              title: '黑名单管理',
              icon: 'stop',
              keepAlive: true
            }
          }
        ]
      },
      {
        path: 'monitoring',
        name: 'Monitoring',
        meta: {
          title: '监控管理',
          icon: 'monitor'
        },
        children: [
          {
            path: 'control',
            name: 'MonitoringControl',
            component: MonitoringControl,
            meta: {
              title: '监控控制台',
              icon: 'control',
              keepAlive: true
            }
          },
          {
            path: 'discovery',
            name: 'DeviceDiscovery',
            component: DeviceDiscovery,
            meta: {
              title: '设备发现',
              icon: 'search',
              keepAlive: true
            }
          }
        ]
      },
      {
        path: 'settings',
        name: 'Settings',
        component: Settings,
        meta: {
          title: '系统设置',
          icon: 'setting',
          keepAlive: true
        }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/error/404.vue'),
    meta: {
      title: '页面不存在',
      hidden: true
    }
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// Navigation guards
router.beforeEach((to, from, next) => {
  // Set page title
  if (to.meta?.title) {
    document.title = `${to.meta.title} - CampusGuard`
  }
  
  next()
})

export default router
