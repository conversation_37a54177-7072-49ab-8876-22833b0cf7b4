<template>
  <div class="device-list">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>设备管理</h1>
      <div class="header-actions">
        <a-button type="primary" @click="showAddModal" :icon="h(PlusOutlined)">
          添加设备
        </a-button>
        <a-button @click="refreshDevices" :loading="loading" :icon="h(ReloadOutlined)">
          刷新
        </a-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <a-card class="filter-card" :bordered="false">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-input
            v-model:value="searchText"
            placeholder="搜索设备名称或IP"
            allow-clear
            @change="handleSearch"
            :prefix="h(SearchOutlined)"
          />
        </a-col>
        <a-col :span="6">
          <a-select
            v-model:value="filterStatus"
            placeholder="设备状态"
            allow-clear
            style="width: 100%"
            @change="handleFilter"
          >
            <a-select-option value="">全部状态</a-select-option>
            <a-select-option value="online">在线</a-select-option>
            <a-select-option value="offline">离线</a-select-option>
            <a-select-option value="warning">警告</a-select-option>
            <a-select-option value="critical">严重</a-select-option>
            <a-select-option value="maintenance">维护中</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="6">
          <a-select
            v-model:value="filterType"
            placeholder="设备类型"
            allow-clear
            style="width: 100%"
            @change="handleFilter"
          >
            <a-select-option value="">全部类型</a-select-option>
            <a-select-option value="switch">交换机</a-select-option>
            <a-select-option value="router">路由器</a-select-option>
            <a-select-option value="firewall">防火墙</a-select-option>
            <a-select-option value="server">服务器</a-select-option>
            <a-select-option value="ap">无线AP</a-select-option>
            <a-select-option value="other">其他</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="6">
          <a-button type="link" @click="showAdvancedFilter = !showAdvancedFilter">
            高级筛选 <DownOutlined v-if="!showAdvancedFilter" /><UpOutlined v-else />
          </a-button>
        </a-col>
      </a-row>

      <!-- 高级筛选 -->
      <a-row v-if="showAdvancedFilter" :gutter="16" style="margin-top: 16px">
        <a-col :span="6">
          <a-input v-model:value="filterLocation" placeholder="位置" allow-clear />
        </a-col>
        <a-col :span="6">
          <a-input v-model:value="filterBuilding" placeholder="楼栋" allow-clear />
        </a-col>
        <a-col :span="6">
          <a-input v-model:value="filterVendor" placeholder="厂商" allow-clear />
        </a-col>
        <a-col :span="6">
          <a-button type="primary" @click="handleFilter">应用筛选</a-button>
          <a-button style="margin-left: 8px" @click="resetFilter">重置</a-button>
        </a-col>
      </a-row>
    </a-card>

    <!-- 统计卡片 -->
    <a-row :gutter="16" class="stats-row">
      <a-col :xs="24" :sm="12" :md="6">
        <a-card :bordered="false" :loading="statsLoading">
          <a-statistic
            title="设备总数"
            :value="deviceStats.total"
            :value-style="{ color: '#1890ff' }"
          >
            <template #prefix>
              <DesktopOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :xs="24" :sm="12" :md="6">
        <a-card :bordered="false" :loading="statsLoading">
          <a-statistic
            title="在线设备"
            :value="deviceStats.online"
            :value-style="{ color: '#52c41a' }"
          >
            <template #prefix>
              <CheckCircleOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :xs="24" :sm="12" :md="6">
        <a-card :bordered="false" :loading="statsLoading">
          <a-statistic
            title="离线设备"
            :value="deviceStats.offline"
            :value-style="{ color: '#ff4d4f' }"
          >
            <template #prefix>
              <CloseCircleOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :xs="24" :sm="12" :md="6">
        <a-card :bordered="false" :loading="statsLoading">
          <a-statistic
            title="告警设备"
            :value="deviceStats.warning + deviceStats.critical"
            :value-style="{ color: '#faad14' }"
          >
            <template #prefix>
              <WarningOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
    </a-row>

    <!-- 设备列表表格 -->
    <a-card :bordered="false" :loading="loading">
      <a-table
        :columns="columns"
        :data-source="filteredDevices"
        :pagination="pagination"
        :row-key="record => record.id"
        @change="handleTableChange"
      >
        <!-- 设备名称 -->
        <template #name="{ record }">
          <router-link :to="`/devices/${record.id}`" class="device-link">
            {{ record.name }}
          </router-link>
        </template>

        <!-- 状态 -->
        <template #status="{ record }">
          <a-tag :color="getStatusColor(record.status)">
            {{ getStatusText(record.status) }}
          </a-tag>
        </template>

        <!-- 设备类型 -->
        <template #device_type="{ record }">
          <span>
            <component :is="getDeviceIcon(record.device_type)" />
            {{ getDeviceTypeText(record.device_type) }}
          </span>
        </template>

        <!-- 健康评分 -->
        <template #health_score="{ record }">
          <a-progress
            :percent="record.health_score"
            :stroke-color="getHealthColor(record.health_score)"
            :format="percent => `${percent}%`"
            size="small"
          />
        </template>

        <!-- 最后上线时间 -->
        <template #last_seen="{ record }">
          <a-tooltip :title="formatDate(record.last_seen)">
            {{ formatRelativeTime(record.last_seen) }}
          </a-tooltip>
        </template>

        <!-- 操作 -->
        <template #action="{ record }">
          <a-space>
            <a-button type="link" size="small" @click="viewDevice(record)">
              <EyeOutlined /> 查看
            </a-button>
            <a-button type="link" size="small" @click="editDevice(record)">
              <EditOutlined /> 编辑
            </a-button>
            <a-dropdown>
              <a-button type="link" size="small">
                更多 <DownOutlined />
              </a-button>
              <template #overlay>
                <a-menu @click="({ key }) => handleMenuClick(key, record)">
                  <a-menu-item key="test">
                    <ApiOutlined /> 测试连接
                  </a-menu-item>
                  <a-menu-item key="collect">
                    <SyncOutlined /> 收集指标
                  </a-menu-item>
                  <a-menu-item key="performance">
                    <LineChartOutlined /> 性能数据
                  </a-menu-item>
                  <a-menu-divider />
                  <a-menu-item key="delete" danger>
                    <DeleteOutlined /> 删除设备
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </a-space>
        </template>
      </a-table>
    </a-card>

    <!-- 添加/编辑设备弹窗 -->
    <a-modal
      v-model:open="modalVisible"
      :title="isEdit ? '编辑设备' : '添加设备'"
      :width="720"
      :confirm-loading="modalLoading"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="设备名称" name="name">
              <a-input v-model:value="formData.name" placeholder="请输入设备名称" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="IP地址" name="ip_address">
              <a-input v-model:value="formData.ip_address" placeholder="例如: ***********" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="设备类型" name="device_type">
              <a-select v-model:value="formData.device_type" placeholder="请选择设备类型">
                <a-select-option value="switch">交换机</a-select-option>
                <a-select-option value="router">路由器</a-select-option>
                <a-select-option value="firewall">防火墙</a-select-option>
                <a-select-option value="server">服务器</a-select-option>
                <a-select-option value="ap">无线AP</a-select-option>
                <a-select-option value="other">其他</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="MAC地址" name="mac_address">
              <a-input v-model:value="formData.mac_address" placeholder="例如: AA:BB:CC:DD:EE:FF" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="厂商" name="vendor">
              <a-input v-model:value="formData.vendor" placeholder="例如: Cisco" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="型号" name="model">
              <a-input v-model:value="formData.model" placeholder="例如: C9300-24T" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="描述" name="description">
          <a-textarea
            v-model:value="formData.description"
            placeholder="设备描述信息"
            :rows="3"
          />
        </a-form-item>

        <a-divider>位置信息</a-divider>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="位置" name="location">
              <a-input v-model:value="formData.location" placeholder="例如: 数据中心" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="楼栋" name="building">
              <a-input v-model:value="formData.building" placeholder="例如: A栋" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="楼层" name="floor">
              <a-input v-model:value="formData.floor" placeholder="例如: 3F" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="房间" name="room">
              <a-input v-model:value="formData.room" placeholder="例如: 301" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-divider>网络配置</a-divider>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="子网" name="subnet">
              <a-input v-model:value="formData.subnet" placeholder="例如: 192.168.1.0/24" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="VLAN ID" name="vlan_id">
              <a-input-number
                v-model:value="formData.vlan_id"
                :min="1"
                :max="4094"
                placeholder="1-4094"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-divider>SNMP配置</a-divider>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="SNMP团体名" name="snmp_community">
              <a-input-password
                v-model:value="formData.snmp_community"
                placeholder="默认: public"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="SNMP版本" name="snmp_version">
              <a-select v-model:value="formData.snmp_version" placeholder="选择SNMP版本">
                <a-select-option value="1">v1</a-select-option>
                <a-select-option value="2c">v2c</a-select-option>
                <a-select-option value="3">v3</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="SNMP端口" name="snmp_port">
          <a-input-number
            v-model:value="formData.snmp_port"
            :min="1"
            :max="65535"
            placeholder="默认: 161"
            style="width: 100%"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, h } from 'vue'
import { useRouter } from 'vue-router'
import { message, Modal } from 'ant-design-vue'
import {
  PlusOutlined,
  ReloadOutlined,
  SearchOutlined,
  DownOutlined,
  UpOutlined,
  DesktopOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  WarningOutlined,
  EyeOutlined,
  EditOutlined,
  ApiOutlined,
  SyncOutlined,
  LineChartOutlined,
  DeleteOutlined,
  ClusterOutlined,
  CloudServerOutlined,
  SafetyCertificateOutlined,
  WifiOutlined
} from '@ant-design/icons-vue'
import { deviceApi } from '@/api/device'
import { useDeviceStore } from '@/stores/device'
import type { Device, DeviceStats } from '@/types/device'
import type { FormInstance } from 'ant-design-vue'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/zh-cn'

dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

const router = useRouter()
const deviceStore = useDeviceStore()

// 状态
const loading = ref(false)
const statsLoading = ref(false)
const devices = ref<Device[]>([])
const deviceStats = ref<DeviceStats>({
  total: 0,
  online: 0,
  offline: 0,
  warning: 0,
  critical: 0,
  maintenance: 0
})

// 搜索和筛选
const searchText = ref('')
const filterStatus = ref('')
const filterType = ref('')
const filterLocation = ref('')
const filterBuilding = ref('')
const filterVendor = ref('')
const showAdvancedFilter = ref(false)

// 分页
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条`
})

// 表格列配置
const columns = [
  {
    title: '设备名称',
    dataIndex: 'name',
    key: 'name',
    slots: { customRender: 'name' },
    sorter: true
  },
  {
    title: 'IP地址',
    dataIndex: 'ip_address',
    key: 'ip_address',
    sorter: true
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    slots: { customRender: 'status' },
    filters: [
      { text: '在线', value: 'online' },
      { text: '离线', value: 'offline' },
      { text: '警告', value: 'warning' },
      { text: '严重', value: 'critical' }
    ]
  },
  {
    title: '设备类型',
    dataIndex: 'device_type',
    key: 'device_type',
    slots: { customRender: 'device_type' }
  },
  {
    title: '厂商',
    dataIndex: 'vendor',
    key: 'vendor'
  },
  {
    title: '位置',
    dataIndex: 'location',
    key: 'location'
  },
  {
    title: '健康评分',
    dataIndex: 'health_score',
    key: 'health_score',
    slots: { customRender: 'health_score' },
    sorter: true
  },
  {
    title: '最后上线',
    dataIndex: 'last_seen',
    key: 'last_seen',
    slots: { customRender: 'last_seen' },
    sorter: true
  },
  {
    title: '操作',
    key: 'action',
    slots: { customRender: 'action' },
    width: 180
  }
]

// 弹窗相关
const modalVisible = ref(false)
const modalLoading = ref(false)
const isEdit = ref(false)
const formRef = ref<FormInstance>()
const formData = ref({
  name: '',
  ip_address: '',
  mac_address: '',
  device_type: 'switch',
  vendor: '',
  model: '',
  description: '',
  location: '',
  building: '',
  floor: '',
  room: '',
  subnet: '',
  vlan_id: undefined as number | undefined,
  snmp_community: 'public',
  snmp_version: '2c',
  snmp_port: 161
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入设备名称', trigger: 'blur' }
  ],
  ip_address: [
    { required: true, message: '请输入IP地址', trigger: 'blur' },
    {
      pattern: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
      message: '请输入有效的IP地址',
      trigger: 'blur'
    }
  ],
  device_type: [
    { required: true, message: '请选择设备类型', trigger: 'change' }
  ],
  mac_address: [
    {
      pattern: /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/,
      message: '请输入有效的MAC地址',
      trigger: 'blur'
    }
  ]
}

// 计算属性
const filteredDevices = computed(() => {
  let result = [...devices.value]

  // 搜索过滤
  if (searchText.value) {
    const search = searchText.value.toLowerCase()
    result = result.filter(device =>
      device.name.toLowerCase().includes(search) ||
      device.ip_address.includes(search)
    )
  }

  // 状态过滤
  if (filterStatus.value) {
    result = result.filter(device => device.status === filterStatus.value)
  }

  // 类型过滤
  if (filterType.value) {
    result = result.filter(device => device.device_type === filterType.value)
  }

  // 高级筛选
  if (filterLocation.value) {
    result = result.filter(device =>
      device.location?.toLowerCase().includes(filterLocation.value.toLowerCase())
    )
  }
  if (filterBuilding.value) {
    result = result.filter(device =>
      device.building?.toLowerCase().includes(filterBuilding.value.toLowerCase())
    )
  }
  if (filterVendor.value) {
    result = result.filter(device =>
      device.vendor?.toLowerCase().includes(filterVendor.value.toLowerCase())
    )
  }

  pagination.value.total = result.length
  return result
})

// 方法
const fetchDevices = async () => {
  loading.value = true
  try {
    const response = await deviceApi.getDevices()
    devices.value = response.data
    deviceStore.setDevices(response.data)
  } catch (error: any) {
    message.error('获取设备列表失败: ' + (error.message || '未知错误'))
  } finally {
    loading.value = false
  }
}

const fetchDeviceStats = async () => {
  statsLoading.value = true
  try {
    const response = await deviceApi.getStats()
    deviceStats.value = response.data
  } catch (error: any) {
    message.error('获取设备统计失败: ' + (error.message || '未知错误'))
  } finally {
    statsLoading.value = false
  }
}

const refreshDevices = () => {
  fetchDevices()
  fetchDeviceStats()
}

const handleSearch = () => {
  pagination.value.current = 1
}

const handleFilter = () => {
  pagination.value.current = 1
}

const resetFilter = () => {
  searchText.value = ''
  filterStatus.value = ''
  filterType.value = ''
  filterLocation.value = ''
  filterBuilding.value = ''
  filterVendor.value = ''
  showAdvancedFilter.value = false
  handleFilter()
}

const handleTableChange = (paginationInfo: any, filters: any, sorter: any) => {
  pagination.value = paginationInfo
}

const showAddModal = () => {
  isEdit.value = false
  formData.value = {
    name: '',
    ip_address: '',
    mac_address: '',
    device_type: 'switch',
    vendor: '',
    model: '',
    description: '',
    location: '',
    building: '',
    floor: '',
    room: '',
    subnet: '',
    vlan_id: undefined,
    snmp_community: 'public',
    snmp_version: '2c',
    snmp_port: 161
  }
  modalVisible.value = true
}

const viewDevice = (device: Device) => {
  router.push(`/devices/${device.id}`)
}

const editDevice = (device: Device) => {
  isEdit.value = true
  formData.value = { ...device }
  modalVisible.value = true
}

const handleModalOk = async () => {
  try {
    await formRef.value?.validateFields()
    modalLoading.value = true

    if (isEdit.value) {
      await deviceApi.updateDevice(formData.value.id, formData.value)
      message.success('设备更新成功')
    } else {
      await deviceApi.createDevice(formData.value)
      message.success('设备添加成功')
    }

    modalVisible.value = false
    refreshDevices()
  } catch (error: any) {
    if (!error.errorFields) {
      message.error('操作失败: ' + (error.message || '未知错误'))
    }
  } finally {
    modalLoading.value = false
  }
}

const handleModalCancel = () => {
  formRef.value?.resetFields()
}

const handleMenuClick = async (key: string, device: Device) => {
  switch (key) {
    case 'test':
      await testConnection(device)
      break
    case 'collect':
      await collectMetrics(device)
      break
    case 'performance':
      router.push(`/performance?device_id=${device.id}`)
      break
    case 'delete':
      await deleteDevice(device)
      break
  }
}

const testConnection = async (device: Device) => {
  try {
    const response = await deviceApi.testConnection(device.id)
    if (response.data.success) {
      message.success(`设备 ${device.name} 连接测试成功`)
    } else {
      message.error(`设备 ${device.name} 连接测试失败: ${response.data.message}`)
    }
  } catch (error: any) {
    message.error('连接测试失败: ' + (error.message || '未知错误'))
  }
}

const collectMetrics = async (device: Device) => {
  try {
    await deviceApi.collectMetrics(device.id)
    message.success(`已开始收集设备 ${device.name} 的性能指标`)
  } catch (error: any) {
    message.error('指标收集失败: ' + (error.message || '未知错误'))
  }
}

const deleteDevice = async (device: Device) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除设备 "${device.name}" 吗？此操作不可恢复。`,
    okText: '确定',
    cancelText: '取消',
    okType: 'danger',
    onOk: async () => {
      try {
        await deviceApi.deleteDevice(device.id)
        message.success('设备删除成功')
        refreshDevices()
      } catch (error: any) {
        message.error('删除失败: ' + (error.message || '未知错误'))
      }
    }
  })
}

// 工具函数
const getStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    online: 'success',
    offline: 'error',
    warning: 'warning',
    critical: 'error',
    maintenance: 'default',
    unknown: 'default'
  }
  return colors[status] || 'default'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    online: '在线',
    offline: '离线',
    warning: '警告',
    critical: '严重',
    maintenance: '维护中',
    unknown: '未知'
  }
  return texts[status] || status
}

const getDeviceTypeText = (type: string) => {
  const types: Record<string, string> = {
    switch: '交换机',
    router: '路由器',
    firewall: '防火墙',
    server: '服务器',
    ap: '无线AP',
    other: '其他'
  }
  return types[type] || type
}

const getDeviceIcon = (type: string) => {
  const icons: Record<string, any> = {
    switch: ClusterOutlined,
    router: CloudServerOutlined,
    firewall: SafetyCertificateOutlined,
    server: DesktopOutlined,
    ap: WifiOutlined,
    other: DesktopOutlined
  }
  return icons[type] || DesktopOutlined
}

const getHealthColor = (score: number) => {
  if (score >= 80) return '#52c41a'
  if (score >= 60) return '#faad14'
  return '#ff4d4f'
}

const formatDate = (date: string) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
}

const formatRelativeTime = (date: string) => {
  return dayjs(date).fromNow()
}

// 生命周期
onMounted(() => {
  refreshDevices()
})
</script>

<style scoped lang="less">
.device-list {
  padding: 16px;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    h1 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }

    .header-actions {
      display: flex;
      gap: 8px;
    }
  }

  .filter-card {
    margin-bottom: 16px;
  }

  .stats-row {
    margin-bottom: 16px;
  }

  .device-link {
    font-weight: 500;
    &:hover {
      text-decoration: underline;
    }
  }
}
</style>