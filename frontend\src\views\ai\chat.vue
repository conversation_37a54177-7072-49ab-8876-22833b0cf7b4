<template>
  <div class="ai-chat">
    <a-row :gutter="16" class="chat-container">
      <!-- Agent Selection Sidebar -->
      <a-col :xs="24" :md="6" class="agent-sidebar">
        <a-card title="AI助手" size="small">
          <!-- Agent Selection -->
          <div class="agent-selection">
            <a-select
              v-model:value="selectedAgent"
              @change="handleAgentChange"
              style="width: 100%"
              size="large"
            >
              <a-select-option
                v-for="agent in availableAgents"
                :key="agent.key"
                :value="agent.key"
              >
                <div class="agent-option">
                  <a-avatar :src="agent.avatar" size="small" style="margin-right: 8px;" />
                  <div class="agent-info">
                    <div class="agent-name">{{ agent.name }}</div>
                    <div class="agent-desc">{{ agent.description }}</div>
                  </div>
                </div>
              </a-select-option>
            </a-select>
          </div>

          <a-divider />

          <!-- Quick Prompts -->
          <div class="quick-prompts">
            <h4>快速提示</h4>
            <div class="prompts-list">
              <a-button
                v-for="prompt in quickPrompts"
                :key="prompt.key"
                type="text"
                size="small"
                @click="handlePromptClick(prompt)"
                class="prompt-button"
              >
                <component :is="prompt.icon" />
                {{ prompt.label }}
              </a-button>
            </div>
          </div>

          <a-divider />

          <!-- Conversation History -->
          <div class="conversation-history">
            <div class="history-header">
              <h4>对话历史</h4>
              <a-space>
                <a-button
                  type="text"
                  size="small"
                  @click="createNewConversation"
                  title="新建对话"
                >
                  <template #icon>
                    <PlusOutlined />
                  </template>
                </a-button>
                <a-button
                  type="text"
                  size="small"
                  @click="loadConversationHistory"
                  :loading="conversationsLoading"
                  title="刷新历史"
                >
                  <template #icon>
                    <ReloadOutlined />
                  </template>
                </a-button>
              </a-space>
            </div>
            
            <div class="conversations-list" v-if="!conversationsLoading">
              <div
                v-for="conversation in conversations"
                :key="conversation.key"
                :class="[
                  'conversation-item',
                  { 'active': activeConversationKey === conversation.key }
                ]"
                @click="switchConversation(conversation.key)"
              >
                <div class="conversation-content">
                  <div class="conversation-title">{{ conversation.title }}</div>
                  <div class="conversation-meta">
                    <span class="message-count">{{ conversation.total_messages || conversation.messages.length }} 条消息</span>
                    <span class="conversation-time">{{ formatTime(conversation.updated_at || conversation.created_at) }}</span>
                  </div>
                </div>
                <a-button
                  v-if="conversations.length > 1"
                  type="text"
                  size="small"
                  danger
                  @click.stop="deleteConversation(conversation.key)"
                  class="delete-btn"
                >
                  <template #icon>
                    <DeleteOutlined />
                  </template>
                </a-button>
              </div>
            </div>
            
            <div v-else class="loading-conversations">
              <a-spin size="small" />
              <span style="margin-left: 8px;">加载中...</span>
            </div>
          </div>

          <a-divider />

          <!-- Connection Status -->
          <div class="connection-status">
            <a-badge
              :status="aiConnected ? 'success' : 'error'"
              :text="aiConnected ? '连接正常' : '连接断开'"
            />
          </div>
        </a-card>
      </a-col>

      <!-- Chat Interface -->
      <a-col :xs="24" :md="18" class="chat-main">
        <a-card class="chat-card">
          <!-- Chat Header -->
          <template #title>
            <div class="chat-header">
              <a-avatar :src="getCurrentAgent().avatar" />
              <div class="agent-info">
                <div class="agent-name">{{ getCurrentAgent().name }}</div>
                <div class="agent-desc">{{ getCurrentAgent().description }}</div>
              </div>
            </div>
          </template>

          <template #extra>
            <a-space>
              <a-button
                type="text"
                :icon="h(ClearOutlined)"
                @click="clearChat"
                title="清空对话"
              />
              <a-button
                type="text"
                :icon="h(DownloadOutlined)"
                @click="exportChat"
                title="导出对话"
              />
            </a-space>
          </template>

          <!-- AI Chat Interface using ant-design-x-vue -->
          <div class="ai-chat-container">
            <!-- Conversations Management -->
            <AXConversations
              :items="conversations"
              :active-key="activeConversationKey"
              @active-change="handleConversationChange"
              @create="handleCreateConversation"
              class="conversations-panel"
            >
              <template #title="{ item }">
                <span>{{ item.title || `与${getCurrentAgent().name}的对话` }}</span>
              </template>
            </AXConversations>

            <!-- Messages Display Area -->
            <div class="messages-container" ref="messagesContainer">
              <AXBubble
                v-for="message in currentMessages"
                :key="message.id"
                :content="message.content"
                :type="message.type"
                :avatar="message.avatar"
                :status="message.status"
                :typing="message.typing"
                :actions="message.type === 'ai' ? bubbleActions : undefined"
                class="message-bubble"
              />

              <!-- Empty State -->
              <div v-if="currentMessages.length === 0" class="empty-state">
                <a-empty description="开始与AI助手对话吧！">
                  <template #image>
                    <RobotOutlined style="font-size: 64px; color: #d9d9d9;" />
                  </template>
                </a-empty>
              </div>
            </div>

            <!-- Input Area using AXPrompt -->
            <AXPrompt
              v-model="inputValue"
              :loading="isLoading"
              :placeholder="`向${getCurrentAgent().name}提问...`"
              :auto-size="{ minRows: 1, maxRows: 4 }"
              @submit="handleSubmit"
              @clear="handleClear"
              class="chat-input-prompt"
            >
              <template #actions>
                <a-space>
                  <a-button
                    type="text"
                    size="small"
                    @click="handleClearHistory"
                    :icon="h(ClearOutlined)"
                  >
                    清空历史
                  </a-button>
                  <a-button
                    type="text"
                    size="small"
                    @click="showQuickPrompts = !showQuickPrompts"
                    :icon="h(RobotOutlined)"
                  >
                    快速提示
                  </a-button>
                </a-space>
              </template>
            </AXPrompt>

            <!-- Quick Prompts Suggestions -->
            <div v-if="showQuickPrompts" class="quick-prompts-overlay">
              <div class="quick-prompts-content">
                <h4>快速提示</h4>
                <div class="prompts-grid">
                  <a-button
                    v-for="prompt in quickPrompts"
                    :key="prompt.key"
                    type="text"
                    size="small"
                    @click="handlePromptClick(prompt)"
                    class="prompt-button"
                  >
                    <component :is="prompt.icon" />
                    {{ prompt.label }}
                  </a-button>
                </div>
              </div>
            </div>
          </div>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, h, computed } from 'vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import { aiApi } from '@/api/ai'
import {
  UserOutlined,
  RobotOutlined,
  ClearOutlined,
  DownloadOutlined,
  SecurityScanOutlined,
  LineChartOutlined,
  AlertOutlined,
  CopyOutlined,
  PlusOutlined,
  ReloadOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'

// Import ant-design-x-vue components
import {
  Conversations as AXConversations,
  Bubble as AXBubble,
  Prompt as AXPrompt
} from 'ant-design-x-vue'

// Types
interface Message {
  id: string
  content: string
  type: 'user' | 'ai'
  avatar?: string
  status?: 'loading' | 'success' | 'error'
  typing?: boolean
  timestamp: string
  agent_type?: string
}

interface Conversation {
  key: string
  title: string
  messages: Message[]
  id?: number | null
  created_at?: string
  updated_at?: string
  total_messages?: number
  status?: string
}

// State
const selectedAgent = ref('general')
const inputValue = ref('')
const isLoading = ref(false)
const aiConnected = ref(true)
const messagesContainer = ref()
const showQuickPrompts = ref(false)

// Conversations management
const conversations = ref<Conversation[]>([])
const activeConversationKey = ref<string>('')
const conversationsLoading = ref(false)
const showConversationHistory = ref(false)

// Suggestion items for auto-completion
const suggestionItems = ref([
  { key: 'device-status', label: '查看设备状态', value: '查看设备状态' },
  { key: 'network-performance', label: '分析网络性能', value: '分析网络性能' },
  { key: 'security-threats', label: '检查安全威胁', value: '检查安全威胁' },
  { key: 'alert-info', label: '查看告警信息', value: '查看告警信息' },
  { key: 'topology-view', label: '查看网络拓扑', value: '查看网络拓扑' },
  { key: 'performance-report', label: '生成性能报告', value: '生成性能报告' }
])

// Available agents (updated to match backend)
const availableAgents = [
  {
    key: 'general',
    name: '通用助手',
    description: '网络监控通用AI助手，提供设备状态、告警概览等基础信息',
    avatar: '/avatars/general.png'
  },
  {
    key: 'security',
    name: '安全专家',
    description: '网络安全分析专家，专注威胁检测、安全事件分析和防护建议',
    avatar: '/avatars/security.png'
  },
  {
    key: 'performance',
    name: '性能专家',
    description: '网络性能分析专家，专注性能监控、瓶颈分析和优化建议',
    avatar: '/avatars/performance.png'
  }
]

// Quick prompts for different agents
const quickPrompts = ref([
  {
    key: 'device-status',
    label: '查看设备状态',
    icon: h(LineChartOutlined)
  },
  {
    key: 'network-performance',
    label: '分析网络性能',
    icon: h(LineChartOutlined)
  },
  {
    key: 'security-threats',
    label: '检查安全威胁',
    icon: h(SecurityScanOutlined)
  },
  {
    key: 'alert-info',
    label: '查看告警信息',
    icon: h(AlertOutlined)
  }
])

// Computed properties
const getCurrentAgent = () => {
  return availableAgents.find(a => a.key === selectedAgent.value) || availableAgents[0]
}

const currentMessages = computed(() => {
  const currentConv = conversations.value.find(
    conv => conv.key === activeConversationKey.value
  )
  return currentConv ? currentConv.messages : []
})

// Bubble actions for AI messages
const bubbleActions = [
  {
    icon: h(CopyOutlined),
    tooltip: '复制',
    onClick: (message: Message) => copyMessage(message.content)
  },
  {
    icon: h(RobotOutlined),
    tooltip: '重新生成',
    onClick: (message: Message) => regenerateMessage(message)
  }
]

// Methods
// Conversation history management
const loadConversationHistory = async () => {
  try {
    conversationsLoading.value = true
    const response = await aiApi.getConversations({
      limit: 20,
      skip: 0
    })
    
    if (response.data && Array.isArray(response.data)) {
      // Convert backend conversations to frontend format
      const backendConversations = response.data.map((conv: any) => ({
        key: conv.id.toString(),
        title: conv.title || `对话 ${conv.id}`,
        messages: [], // Messages will be loaded when conversation is selected
        id: conv.id,
        created_at: conv.started_at || conv.created_at,
        updated_at: conv.last_activity || conv.updated_at,
        total_messages: conv.total_messages || 0,
        status: conv.status
      }))
      
      // Add default conversation if no conversations exist
      if (backendConversations.length === 0) {
        conversations.value = [{
          key: 'default',
          title: '新对话',
          messages: [],
          id: null,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          total_messages: 0,
          status: 'active'
        }]
        activeConversationKey.value = 'default'
      } else {
        conversations.value = backendConversations
        // Set active conversation to the most recent one
        activeConversationKey.value = backendConversations[0].key
      }
    }
  } catch (error: any) {
    console.error('加载对话历史失败:', error)
    message.error('加载对话历史失败: ' + (error.message || '未知错误'))
    // Fallback to default conversation
    conversations.value = [{
      key: 'default',
      title: '新对话',
      messages: [],
      id: null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      total_messages: 0,
      status: 'active'
    }]
    activeConversationKey.value = 'default'
  } finally {
    conversationsLoading.value = false
  }
}

const loadConversationMessages = async (conversationId: number) => {
  try {
    const response = await aiApi.getConversationMessages(conversationId)
    
    if (response.data && response.data.messages) {
      // Convert backend messages to frontend format
      const backendMessages = response.data.messages.map((msg: any) => ({
        id: msg.id.toString(),
        content: msg.content,
        type: msg.role === 'user' ? 'user' : 'ai',
        role: msg.role === 'user' ? 'user' : 'assistant',
        avatar: msg.role === 'user' ? undefined : getCurrentAgent().avatar,
        status: 'success',
        timestamp: msg.timestamp,
        agent_type: selectedAgent.value
      }))
      
      // Update the conversation with loaded messages
      const conv = conversations.value.find(c => c.key === conversationId.toString())
      if (conv) {
        conv.messages = backendMessages
      }
    }
  } catch (error: any) {
    console.error('加载对话消息失败:', error)
    message.error('加载对话消息失败: ' + (error.message || '未知错误'))
  }
}

const createNewConversation = () => {
  const newConversationKey = `new_${Date.now()}`
  const newConversation = {
    key: newConversationKey,
    title: '新对话',
    messages: [],
    id: null,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    total_messages: 0,
    status: 'active'
  }
  
  conversations.value.unshift(newConversation)
  activeConversationKey.value = newConversationKey
  addWelcomeMessage()
}

const switchConversation = async (conversationKey: string) => {
  activeConversationKey.value = conversationKey
  
  const conversation = conversations.value.find(c => c.key === conversationKey)
  if (conversation && conversation.id && conversation.messages.length === 0) {
    // Load messages for this conversation if not loaded yet
    await loadConversationMessages(conversation.id)
  }
  
  // If it's a new conversation without messages, add welcome message
  if (conversation && conversation.messages.length === 0) {
    addWelcomeMessage()
  }
}

const deleteConversation = (conversationKey: string) => {
  if (conversations.value.length <= 1) {
    message.warning('至少需要保留一个对话')
    return
  }
  
  const index = conversations.value.findIndex(c => c.key === conversationKey)
  if (index !== -1) {
    conversations.value.splice(index, 1)
    
    // If deleted conversation was active, switch to another one
    if (activeConversationKey.value === conversationKey) {
      activeConversationKey.value = conversations.value[0].key
    }
  }
}

const formatTime = (timestamp: string) => {
  return dayjs(timestamp).format('HH:mm:ss')
}

const handleAgentChange = () => {
  // Create new conversation when switching agents
  createNewConversation()
}

const addWelcomeMessage = () => {
  const welcomeMessages = {
    general: '您好！我是CampusGuard通用助手，可以帮您查看设备状态、分析网络问题。有什么可以帮您的吗？',
    security: '您好！我是安全分析专家，专门负责网络安全威胁检测和分析。请告诉我您需要分析什么安全问题？',
    performance: '您好！我是性能分析专家，专门负责网络性能监控和优化建议。请告诉我您需要分析什么性能问题？'
  }

  const currentConv = conversations.value.find(
    conv => conv.key === activeConversationKey.value
  )

  if (currentConv) {
    const welcomeMessage: Message = {
      id: Date.now().toString(),
      content: welcomeMessages[selectedAgent.value as keyof typeof welcomeMessages] || welcomeMessages.general,
      type: 'ai',
      avatar: getCurrentAgent().avatar,
      status: 'success',
      timestamp: new Date().toISOString(),
      agent_type: selectedAgent.value
    }

    currentConv.messages.push(welcomeMessage)
    scrollToBottom()
  }
}

const handlePromptClick = (item: any) => {
  const promptMessages = {
    'device-status': '查看设备状态',
    'network-performance': '分析网络性能',
    'security-threats': '检查安全威胁',
    'alert-info': '查看告警信息'
  }

  inputValue.value = promptMessages[item.key as keyof typeof promptMessages] || item.label
  showQuickPrompts.value = false
  handleSubmit(inputValue.value)
}

// New conversation management methods
const handleConversationChange = (key: string) => {
  activeConversationKey.value = key
}

const handleCreateConversation = () => {
  const newKey = `conv_${Date.now()}`
  const newConversation: Conversation = {
    key: newKey,
    title: `与${getCurrentAgent().name}的对话`,
    messages: []
  }
  conversations.value.push(newConversation)
  activeConversationKey.value = newKey
  addWelcomeMessage()
}

// Main submit handler for AXPrompt
const handleSubmit = async (value: string) => {
  if (!value.trim() || isLoading.value) return

  isLoading.value = true

  // Get current conversation
  const currentConv = conversations.value.find(
    conv => conv.key === activeConversationKey.value
  )

  if (!currentConv) return

  // Add user message
  const userMessage: Message = {
    id: Date.now().toString(),
    content: value,
    type: 'user',
    avatar: '/avatars/user.png',
    status: 'success',
    timestamp: new Date().toISOString()
  }
  currentConv.messages.push(userMessage)

  // Add AI thinking status
  const aiMessage: Message = {
    id: (Date.now() + 1).toString(),
    content: '',
    type: 'ai',
    avatar: getCurrentAgent().avatar,
    status: 'loading',
    typing: true,
    timestamp: new Date().toISOString(),
    agent_type: selectedAgent.value
  }
  currentConv.messages.push(aiMessage)

  await scrollToBottom()

  try {
    // Call AI API with conversation context
    const conversationId = currentConv.id ? currentConv.id.toString() : undefined
    const response = await aiApi.chatWithAgent(selectedAgent.value, value, conversationId)

    if (response.data && response.data.response) {
      // Update AI reply
      aiMessage.content = response.data.response
      aiMessage.status = 'success'
      aiMessage.typing = false
      
      // Update conversation metadata
      if (response.data.conversation_id && !currentConv.id) {
        currentConv.id = response.data.conversation_id
        currentConv.key = response.data.conversation_id.toString()
      }
      
      // Update conversation title if it's a new conversation
      if (currentConv.title === '新对话' && value.length > 0) {
        currentConv.title = value.length > 30 ? value.substring(0, 30) + '...' : value
      }
      
      // Update message count
      currentConv.total_messages = (currentConv.total_messages || 0) + 2
      currentConv.updated_at = new Date().toISOString()
    } else {
      aiMessage.content = '抱歉，AI服务暂时不可用，请稍后重试。'
      aiMessage.status = 'error'
      aiMessage.typing = false
    }
  } catch (error: any) {
    aiMessage.content = `抱歉，发生了错误：${error.message || '未知错误'}`
    aiMessage.status = 'error'
    aiMessage.typing = false
    message.error('发送消息失败')
  } finally {
    isLoading.value = false
    await scrollToBottom()
  }
}

const handleClear = () => {
  inputValue.value = ''
}

const scrollToBottom = async () => {
  await nextTick()
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

const copyMessage = async (content: string) => {
  try {
    await navigator.clipboard.writeText(content)
    message.success('已复制到剪贴板')
  } catch (error) {
    message.error('复制失败')
  }
}

const regenerateMessage = async (messageToRegenerate: Message) => {
  // Find the current conversation
  const currentConv = conversations.value.find(
    conv => conv.key === activeConversationKey.value
  )

  if (!currentConv) return

  // Find the user message that triggered this AI response
  const messageIndex = currentConv.messages.findIndex(m => m.id === messageToRegenerate.id)
  if (messageIndex > 0) {
    const userMessage = currentConv.messages[messageIndex - 1]
    if (userMessage.type === 'user') {
      // Remove the AI message and regenerate
      currentConv.messages.splice(messageIndex, 1)
      await handleSubmit(userMessage.content)
    }
  }
}

const handleClearHistory = () => {
  const currentConv = conversations.value.find(
    conv => conv.key === activeConversationKey.value
  )
  if (currentConv) {
    currentConv.messages = []
    addWelcomeMessage()
  }
}

const clearChat = () => {
  handleClearHistory()
}

const exportChat = () => {
  const formatTimeLocal = (timestamp: string) => {
    return dayjs(timestamp).format('HH:mm:ss')
  }

  const currentConv = conversations.value.find(
    conv => conv.key === activeConversationKey.value
  )

  if (!currentConv) return

  const chatContent = currentConv.messages
    .filter(m => !m.typing)
    .map(m => `[${formatTimeLocal(m.timestamp)}] ${m.type === 'user' ? '用户' : getCurrentAgent().name}: ${m.content}`)
    .join('\n\n')

  const blob = new Blob([chatContent], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `chat-${dayjs().format('YYYY-MM-DD-HH-mm-ss')}.txt`
  a.click()
  URL.revokeObjectURL(url)
}

// Lifecycle
onMounted(async () => {
  await loadConversationHistory()
  // Add welcome message only if no conversations were loaded
  if (conversations.value.length === 1 && conversations.value[0].messages.length === 0) {
    addWelcomeMessage()
  }
})
</script>

<style scoped>
.ai-chat {
  padding: 24px;
  background: var(--bg-color);
  min-height: 100vh;
}

.chat-container {
  height: calc(100vh - 48px);
}

.agent-sidebar {
  height: 100%;
}

.agent-option {
  margin-bottom: 8px;
  height: auto;
  padding: 12px;
  text-align: left;
}

.agent-info .agent-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.agent-info .agent-desc {
  font-size: 12px;
  color: var(--text-secondary);
}

.quick-prompts h4 {
  margin-bottom: 12px;
  font-size: 14px;
}

.prompt-button {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  width: 100%;
  text-align: left;
}

.chat-main {
  height: 100%;
}

.chat-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chat-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.chat-header .agent-info .agent-name {
  font-weight: 500;
}

.chat-header .agent-info .agent-desc {
  font-size: 12px;
  color: var(--text-secondary);
}

/* AI Chat Container Styles */
.ai-chat-container {
  height: calc(100vh - 200px);
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.conversations-panel {
  max-height: 120px;
  overflow-y: auto;
  border: 1px solid var(--border-color-light);
  border-radius: 8px;
  padding: 8px;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px 0;
  max-height: calc(100vh - 400px);
  border: 1px solid var(--border-color-light);
  border-radius: 8px;
  padding: 16px;
}

.message-bubble {
  margin-bottom: 16px;
}

.chat-input-prompt {
  border-top: 1px solid var(--border-color-light);
  padding-top: 16px;
}

.quick-prompts-overlay {
  position: absolute;
  bottom: 100px;
  left: 0;
  right: 0;
  background: var(--card-bg);
  border: 1px solid var(--border-color-light);
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.quick-prompts-content h4 {
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 500;
}

.prompts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 8px;
}

.prompts-grid .prompt-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.2s;
}

.prompts-grid .prompt-button:hover {
  background-color: var(--hover-bg);
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  text-align: center;
}

/* Conversation History Styles */
.conversation-history {
  margin-bottom: 16px;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.history-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
}

.conversations-list {
  max-height: 200px;
  overflow-y: auto;
}

.conversation-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  margin-bottom: 4px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid transparent;
}

.conversation-item:hover {
  background-color: var(--hover-bg);
  border-color: var(--border-color-light);
}

.conversation-item.active {
  background-color: var(--primary-color-light);
  border-color: var(--primary-color);
}

.conversation-content {
  flex: 1;
  min-width: 0;
}

.conversation-title {
  font-size: 13px;
  font-weight: 500;
  margin-bottom: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.conversation-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 11px;
  color: var(--text-secondary);
}

.message-count {
  margin-right: 8px;
}

.conversation-time {
  font-size: 10px;
}

.delete-btn {
  opacity: 0;
  transition: opacity 0.2s;
  margin-left: 8px;
}

.conversation-item:hover .delete-btn {
  opacity: 1;
}

.loading-conversations {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: var(--text-secondary);
}

.connection-status {
  margin-top: 16px;
}

/* Responsive */
@media (max-width: 768px) {
  .ai-chat {
    padding: 16px;
  }

  .chat-container {
    height: auto;
  }

  .agent-sidebar {
    margin-bottom: 16px;
  }

  .ai-chat-container {
    height: calc(100vh - 150px);
  }

  .messages-container {
    max-height: calc(100vh - 350px);
  }

  .conversations-panel {
    max-height: 80px;
  }

  .prompts-grid {
    grid-template-columns: 1fr;
  }

  .quick-prompts-overlay {
    bottom: 80px;
  }
}
</style>
