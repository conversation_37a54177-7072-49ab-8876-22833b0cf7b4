# CampusGuard Backend Dependencies
# Based on Context7 verification results

# Web Framework
fastapi==0.115.14
uvicorn[standard]==0.35.0

# Database
sqlalchemy==2.0.41
pymysql==1.1.1
alembic==1.16.2

# AI and Agents
openai-agents[litellm]==0.1.0

# Network Monitoring
pysnmp==4.4.12
python3-nmap==1.9.2

# Logging
loguru==0.7.3

# Configuration
python-dotenv==1.1.1
pydantic==2.11.7
pydantic-settings==2.10.1

# WebSocket (using FastAPI built-in WebSocket support)
# websockets==15.0  # Removed: Using FastAPI built-in WebSocket

# HTTP Client (for future external API integrations)
# httpx==0.28.1  # Optional: For async HTTP requests to external APIs
# requests==2.32.4  # Optional: For sync HTTP requests to external APIs