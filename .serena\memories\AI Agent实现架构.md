# CampusGuard AI Agent实现架构

## OpenAI Agents框架集成
基于openai-agents库实现多Agent系统，每个Agent具有专门化的功能和工具。

## 三个核心Agent

### 1. SecurityAnalystAgent（安全分析专家）
**核心功能**：
- check_ip_reputation：检查IP信誉和威胁情报
- get_security_alerts：获取安全告警信息
- get_threat_statistics：获取威胁统计数据
- analyze_security_incident：分析安全事件
- get_blacklist_info：获取黑名单信息

**特点**：
- 专注于威胁检测和安全分析
- 提供专业的安全建议
- 支持中文自然语言交互

### 2. PerformanceAnalystAgent（性能分析专家）
**核心功能**：
- get_performance_overview：获取性能概览
- analyze_device_performance：分析设备性能
- get_top_performance_issues：获取主要性能问题
- recommend_performance_optimization：提供优化建议

**特点**：
- 专注于网络性能分析
- 提供性能优化建议
- 识别性能瓶颈

### 3. GeneralAssistantAgent（通用助手）
**核心功能**：
- get_device_status：获取设备状态
- get_alert_summary：获取告警摘要
- get_performance_metrics：获取性能指标
- get_network_topology_info：获取网络拓扑信息

**特点**：
- 处理日常运维查询
- 提供基础信息查询
- 作为默认对话Agent

## Agent管理架构
```python
# agents/__init__.py 中的核心实现
AGENTS = {
    "general_assistant": GeneralAssistantAgent,
    "security_analyst": SecurityAnalystAgent,
    "performance_analyst": PerformanceAnalystAgent
}

# 统一的Agent调用接口
async def chat_with_agent(
    agent_type: str,
    message: str,
    conversation_id: Optional[str] = None,
    context: Optional[Dict] = None
) -> Dict[str, Any]
```

## DeepSeek V3集成配置
- **API端点**：https://api.deepseek.com/v1
- **模型**：deepseek-chat
- **超时设置**：60-120秒
- **温度参数**：0.7（平衡创造性和准确性）

## 工具函数设计原则
1. **返回JSON格式**：所有工具函数返回JSON字符串，便于Agent解析
2. **错误处理**：完善的异常捕获和错误信息返回
3. **中文友好**：支持中文输入输出，ensure_ascii=False
4. **异步实现**：使用async/await支持并发调用

## API集成要点
- 通过FastAPI的/api/v1/agents/chat端点提供服务
- 支持会话管理和上下文保持
- WebSocket支持实时对话推送
- 完整的错误处理和重试机制

## 性能优化
- Agent实例复用，避免重复初始化
- 工具函数结果缓存（适用于静态数据）
- 并发请求限制，防止API超载
- 智能超时和重试策略