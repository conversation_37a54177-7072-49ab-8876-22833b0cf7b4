<template>
  <div class="settings-page">
    <h1>系统设置</h1>
    
    <a-tabs v-model:activeKey="activeTab" type="card">
      <!-- 基本设置 -->
      <a-tab-pane key="basic" tab="基本设置">
        <a-card :bordered="false">
          <a-form
            :model="basicSettings"
            :label-col="{ span: 6 }"
            :wrapper-col="{ span: 14 }"
            @finish="saveBasicSettings"
          >
            <a-form-item label="系统名称" name="systemName">
              <a-input v-model:value="basicSettings.systemName" placeholder="CampusGuard" />
            </a-form-item>
            
            <a-form-item label="系统描述" name="systemDescription">
              <a-textarea
                v-model:value="basicSettings.systemDescription"
                placeholder="校园网络安全监控平台"
                :rows="3"
              />
            </a-form-item>
            
            <a-form-item label="时区设置" name="timezone">
              <a-select v-model:value="basicSettings.timezone">
                <a-select-option value="Asia/Shanghai">Asia/Shanghai (GMT+8)</a-select-option>
                <a-select-option value="Asia/Hong_Kong">Asia/Hong_Kong (GMT+8)</a-select-option>
                <a-select-option value="UTC">UTC (GMT+0)</a-select-option>
              </a-select>
            </a-form-item>
            
            <a-form-item label="语言设置" name="language">
              <a-select v-model:value="basicSettings.language">
                <a-select-option value="zh-CN">简体中文</a-select-option>
                <a-select-option value="en-US">English</a-select-option>
              </a-select>
            </a-form-item>
            
            <a-form-item label="主题设置" name="theme">
              <a-radio-group v-model:value="basicSettings.theme">
                <a-radio value="light">浅色主题</a-radio>
                <a-radio value="dark">深色主题</a-radio>
                <a-radio value="auto">跟随系统</a-radio>
              </a-radio-group>
            </a-form-item>
            
            <a-form-item :wrapper-col="{ offset: 6, span: 14 }">
              <a-button type="primary" html-type="submit" :loading="saving">保存设置</a-button>
            </a-form-item>
          </a-form>
        </a-card>
      </a-tab-pane>
      
      <!-- 监控设置 -->
      <a-tab-pane key="monitoring" tab="监控设置">
        <a-card :bordered="false">
          <a-form
            :model="monitoringSettings"
            :label-col="{ span: 6 }"
            :wrapper-col="{ span: 14 }"
            @finish="saveMonitoringSettings"
          >
            <a-form-item label="监控间隔" name="interval">
              <a-input-number
                v-model:value="monitoringSettings.interval"
                :min="10"
                :max="3600"
                :formatter="value => `${value} 秒`"
                :parser="value => value.replace(' 秒', '')"
                style="width: 200px"
              />
              <span style="margin-left: 8px; color: #999">建议值：30-300秒</span>
            </a-form-item>
            
            <a-form-item label="启用SNMP" name="snmpEnabled">
              <a-switch v-model:checked="monitoringSettings.snmpEnabled" />
            </a-form-item>
            
            <a-form-item label="SNMP版本" name="snmpVersion" v-if="monitoringSettings.snmpEnabled">
              <a-select v-model:value="monitoringSettings.snmpVersion">
                <a-select-option value="1">v1</a-select-option>
                <a-select-option value="2c">v2c</a-select-option>
                <a-select-option value="3">v3</a-select-option>
              </a-select>
            </a-form-item>
            
            <a-form-item label="默认团体名" name="snmpCommunity" v-if="monitoringSettings.snmpEnabled">
              <a-input-password v-model:value="monitoringSettings.snmpCommunity" placeholder="public" />
            </a-form-item>
            
            <a-form-item label="超时设置" name="timeout">
              <a-input-number
                v-model:value="monitoringSettings.timeout"
                :min="1"
                :max="30"
                :formatter="value => `${value} 秒`"
                :parser="value => value.replace(' 秒', '')"
                style="width: 200px"
              />
            </a-form-item>
            
            <a-form-item label="重试次数" name="retries">
              <a-input-number
                v-model:value="monitoringSettings.retries"
                :min="0"
                :max="10"
                style="width: 200px"
              />
            </a-form-item>
            
            <a-form-item label="启用自动发现" name="autoDiscovery">
              <a-switch v-model:checked="monitoringSettings.autoDiscovery" />
              <span style="margin-left: 8px; color: #999">自动发现网络中的新设备</span>
            </a-form-item>
            
            <a-form-item :wrapper-col="{ offset: 6, span: 14 }">
              <a-button type="primary" html-type="submit" :loading="saving">保存设置</a-button>
            </a-form-item>
          </a-form>
        </a-card>
      </a-tab-pane>
      
      <!-- 告警设置 -->
      <a-tab-pane key="alert" tab="告警设置">
        <a-card :bordered="false">
          <a-form
            :model="alertSettings"
            :label-col="{ span: 6 }"
            :wrapper-col="{ span: 14 }"
            @finish="saveAlertSettings"
          >
            <a-form-item label="启用告警" name="enabled">
              <a-switch v-model:checked="alertSettings.enabled" />
            </a-form-item>
            
            <a-form-item label="告警级别阈值" name="thresholds">
              <a-space direction="vertical" style="width: 100%">
                <div>
                  <span style="display: inline-block; width: 100px">CPU使用率：</span>
                  <a-input-number
                    v-model:value="alertSettings.thresholds.cpu"
                    :min="50"
                    :max="100"
                    :formatter="value => `${value}%`"
                    :parser="value => value.replace('%', '')"
                  />
                </div>
                <div>
                  <span style="display: inline-block; width: 100px">内存使用率：</span>
                  <a-input-number
                    v-model:value="alertSettings.thresholds.memory"
                    :min="50"
                    :max="100"
                    :formatter="value => `${value}%`"
                    :parser="value => value.replace('%', '')"
                  />
                </div>
                <div>
                  <span style="display: inline-block; width: 100px">磁盘使用率：</span>
                  <a-input-number
                    v-model:value="alertSettings.thresholds.disk"
                    :min="50"
                    :max="100"
                    :formatter="value => `${value}%`"
                    :parser="value => value.replace('%', '')"
                  />
                </div>
              </a-space>
            </a-form-item>
            
            <a-form-item label="告警通知方式" name="notifications">
              <a-checkbox-group v-model:value="alertSettings.notifications">
                <a-checkbox value="browser">浏览器通知</a-checkbox>
                <a-checkbox value="email">邮件通知</a-checkbox>
                <a-checkbox value="sms">短信通知</a-checkbox>
                <a-checkbox value="webhook">Webhook</a-checkbox>
              </a-checkbox-group>
            </a-form-item>
            
            <a-form-item label="邮件设置" v-if="alertSettings.notifications.includes('email')">
              <a-input
                v-model:value="alertSettings.emailRecipients"
                placeholder="多个邮箱用逗号分隔"
              />
            </a-form-item>
            
            <a-form-item label="Webhook URL" v-if="alertSettings.notifications.includes('webhook')">
              <a-input
                v-model:value="alertSettings.webhookUrl"
                placeholder="https://example.com/webhook"
              />
            </a-form-item>
            
            <a-form-item label="告警抑制" name="suppression">
              <a-input-number
                v-model:value="alertSettings.suppressionTime"
                :min="0"
                :max="3600"
                :formatter="value => `${value} 分钟`"
                :parser="value => value.replace(' 分钟', '')"
                style="width: 200px"
              />
              <span style="margin-left: 8px; color: #999">相同告警的抑制时间</span>
            </a-form-item>
            
            <a-form-item :wrapper-col="{ offset: 6, span: 14 }">
              <a-button type="primary" html-type="submit" :loading="saving">保存设置</a-button>
            </a-form-item>
          </a-form>
        </a-card>
      </a-tab-pane>
      
      <!-- AI设置 -->
      <a-tab-pane key="ai" tab="AI设置">
        <a-card :bordered="false">
          <a-form
            :model="aiSettings"
            :label-col="{ span: 6 }"
            :wrapper-col="{ span: 14 }"
            @finish="saveAISettings"
          >
            <a-form-item label="启用AI功能" name="enabled">
              <a-switch v-model:checked="aiSettings.enabled" />
            </a-form-item>
            
            <a-form-item label="AI模型" name="model">
              <a-select v-model:value="aiSettings.model">
                <a-select-option value="deepseek-chat">DeepSeek Chat</a-select-option>
                <a-select-option value="deepseek-coder">DeepSeek Coder</a-select-option>
                <a-select-option value="gpt-3.5-turbo">GPT-3.5 Turbo</a-select-option>
                <a-select-option value="gpt-4">GPT-4</a-select-option>
              </a-select>
            </a-form-item>
            
            <a-form-item label="API密钥" name="apiKey">
              <a-input-password
                v-model:value="aiSettings.apiKey"
                placeholder="请输入API密钥"
              />
            </a-form-item>
            
            <a-form-item label="API端点" name="apiEndpoint">
              <a-input
                v-model:value="aiSettings.apiEndpoint"
                placeholder="https://api.deepseek.com"
              />
            </a-form-item>
            
            <a-form-item label="最大令牌数" name="maxTokens">
              <a-input-number
                v-model:value="aiSettings.maxTokens"
                :min="100"
                :max="4096"
                style="width: 200px"
              />
            </a-form-item>
            
            <a-form-item label="温度参数" name="temperature">
              <a-slider
                v-model:value="aiSettings.temperature"
                :min="0"
                :max="2"
                :step="0.1"
                :marks="{ 0: '0', 1: '1', 2: '2' }"
              />
              <span style="color: #999">控制AI回复的创造性，0为最保守，2为最有创造性</span>
            </a-form-item>
            
            <a-form-item label="启用Agent" name="agents">
              <a-checkbox-group v-model:value="aiSettings.agents">
                <a-checkbox value="general">通用助手</a-checkbox>
                <a-checkbox value="security">安全分析</a-checkbox>
                <a-checkbox value="performance">性能分析</a-checkbox>
              </a-checkbox-group>
            </a-form-item>
            
            <a-form-item label="Agent管理" name="agentManagement">
              <div class="agent-management">
                <div class="agent-actions">
                  <a-space>
                    <a-button 
                      @click="loadAvailableAgents" 
                      :loading="loadingAgents"
                      type="default"
                    >
                      <template #icon>
                        <ReloadOutlined />
                      </template>
                      刷新Agent列表
                    </a-button>
                    <a-tag color="blue">
                      共 {{ Object.keys(availableAgents).length }} 个Agent
                    </a-tag>
                  </a-space>
                </div>
                
                <div class="agents-list" v-if="Object.keys(availableAgents).length > 0">
                  <a-row :gutter="[16, 16]" style="margin-top: 16px;">
                    <a-col 
                      v-for="(agent, key) in availableAgents" 
                      :key="key"
                      :xs="24" 
                      :sm="12" 
                      :lg="8"
                    >
                      <a-card size="small" :title="agent.name" class="agent-card">
                        <template #extra>
                          <a-tag 
                            :color="aiSettings.agents.includes(key) ? 'green' : 'default'"
                          >
                            {{ aiSettings.agents.includes(key) ? '已启用' : '未启用' }}
                          </a-tag>
                        </template>
                        
                        <div class="agent-info">
                          <p class="agent-instructions">{{ agent.instructions }}</p>
                          <div class="agent-meta">
                            <a-space>
                              <span>
                                <ToolOutlined />
                                {{ agent.tools_count }} 个工具
                              </span>
                            </a-space>
                          </div>
                        </div>
                      </a-card>
                    </a-col>
                  </a-row>
                </div>
                
                <div v-else-if="!loadingAgents" class="no-agents">
                  <a-empty description="暂无Agent信息，请点击刷新按钮加载" />
                </div>
              </div>
            </a-form-item>
            
            <a-form-item :wrapper-col="{ offset: 6, span: 14 }">
              <a-space>
                <a-button type="primary" html-type="submit" :loading="saving">保存设置</a-button>
                <a-button @click="testAIConnection" :loading="testing">测试连接</a-button>
              </a-space>
            </a-form-item>
          </a-form>
        </a-card>
      </a-tab-pane>
      
      <!-- 关于系统 -->
      <a-tab-pane key="about" tab="关于系统">
        <a-card :bordered="false">
          <a-descriptions :column="1" bordered>
            <a-descriptions-item label="系统名称">CampusGuard</a-descriptions-item>
            <a-descriptions-item label="版本号">v1.0.0</a-descriptions-item>
            <a-descriptions-item label="构建时间">{{ buildTime }}</a-descriptions-item>
            <a-descriptions-item label="前端框架">Vue.js 3.5.17</a-descriptions-item>
            <a-descriptions-item label="UI组件库">Ant Design Vue 4.2.6</a-descriptions-item>
            <a-descriptions-item label="后端框架">FastAPI 0.115.14</a-descriptions-item>
            <a-descriptions-item label="数据库">MySQL 8.0</a-descriptions-item>
            <a-descriptions-item label="AI模型">DeepSeek V3</a-descriptions-item>
            <a-descriptions-item label="开发团队">CampusGuard Team</a-descriptions-item>
            <a-descriptions-item label="许可证">MIT License</a-descriptions-item>
          </a-descriptions>
          
          <a-divider>系统状态</a-divider>
          
          <a-row :gutter="16">
            <a-col :span="8">
              <a-card>
                <a-statistic
                  title="系统运行时间"
                  :value="uptime"
                  suffix="小时"
                />
              </a-card>
            </a-col>
            <a-col :span="8">
              <a-card>
                <a-statistic
                  title="API版本"
                  value="v1"
                  :value-style="{ color: '#3f8600' }"
                />
              </a-card>
            </a-col>
            <a-col :span="8">
              <a-card>
                <a-statistic
                  title="WebSocket状态"
                  :value="wsConnected ? '已连接' : '未连接'"
                  :value-style="{ color: wsConnected ? '#3f8600' : '#cf1322' }"
                />
              </a-card>
            </a-col>
          </a-row>
          
          <a-divider>更新日志</a-divider>
          
          <a-timeline>
            <a-timeline-item color="green">
              <p>v1.0.0 - 2025-01-03</p>
              <p>- 初始版本发布</p>
              <p>- 支持设备监控、告警管理</p>
              <p>- 集成AI智能分析</p>
              <p>- 实时WebSocket通信</p>
            </a-timeline-item>
          </a-timeline>
        </a-card>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { useWebSocketStore } from '@/stores/websocket'
import { aiApi } from '@/api/ai'
import dayjs from 'dayjs'
import { ReloadOutlined, ToolOutlined } from '@ant-design/icons-vue'

const wsStore = useWebSocketStore()

// 当前标签页
const activeTab = ref('basic')

// 加载状态
const saving = ref(false)
const testing = ref(false)
const loadingAgents = ref(false)

// Agent管理状态
const availableAgents = ref<Record<string, any>>({})

// 基本设置
const basicSettings = ref({
  systemName: 'CampusGuard',
  systemDescription: '校园网络安全监控平台',
  timezone: 'Asia/Shanghai',
  language: 'zh-CN',
  theme: 'light'
})

// 监控设置
const monitoringSettings = ref({
  interval: 30,
  snmpEnabled: true,
  snmpVersion: '2c',
  snmpCommunity: 'public',
  timeout: 5,
  retries: 3,
  autoDiscovery: false
})

// 告警设置
const alertSettings = ref({
  enabled: true,
  thresholds: {
    cpu: 80,
    memory: 85,
    disk: 90
  },
  notifications: ['browser'],
  emailRecipients: '',
  webhookUrl: '',
  suppressionTime: 30
})

// AI设置
const aiSettings = ref({
  enabled: true,
  model: 'deepseek-chat',
  apiKey: '',
  apiEndpoint: 'https://api.deepseek.com',
  maxTokens: 2048,
  temperature: 0.7,
  agents: ['general', 'security', 'performance']
})

// 计算属性
const buildTime = computed(() => {
  return dayjs().format('YYYY-MM-DD HH:mm:ss')
})

const uptime = computed(() => {
  // TODO: 从后端获取实际运行时间
  return 24
})

const wsConnected = computed(() => {
  return wsStore.connected
})

// 方法
const saveBasicSettings = async () => {
  saving.value = true
  try {
    // TODO: 调用API保存设置
    await new Promise(resolve => setTimeout(resolve, 1000))
    message.success('基本设置保存成功')
  } catch (error: any) {
    message.error('保存失败: ' + (error.message || '未知错误'))
  } finally {
    saving.value = false
  }
}

const saveMonitoringSettings = async () => {
  saving.value = true
  try {
    // TODO: 调用API保存设置
    await new Promise(resolve => setTimeout(resolve, 1000))
    message.success('监控设置保存成功')
  } catch (error: any) {
    message.error('保存失败: ' + (error.message || '未知错误'))
  } finally {
    saving.value = false
  }
}

const saveAlertSettings = async () => {
  saving.value = true
  try {
    // TODO: 调用API保存设置
    await new Promise(resolve => setTimeout(resolve, 1000))
    message.success('告警设置保存成功')
  } catch (error: any) {
    message.error('保存失败: ' + (error.message || '未知错误'))
  } finally {
    saving.value = false
  }
}

const saveAISettings = async () => {
  saving.value = true
  try {
    // TODO: 调用API保存设置
    await new Promise(resolve => setTimeout(resolve, 1000))
    message.success('AI设置保存成功')
  } catch (error: any) {
    message.error('保存失败: ' + (error.message || '未知错误'))
  } finally {
    saving.value = false
  }
}

const testAIConnection = async () => {
  testing.value = true
  try {
    const response = await aiApi.testConnection()
    if (response.data.success) {
      message.success('AI连接测试成功: ' + response.data.message)
    } else {
      message.error('AI连接测试失败: ' + response.data.message)
    }
  } catch (error: any) {
    message.error('测试失败: ' + (error.message || '未知错误'))
  } finally {
    testing.value = false
  }
}

const loadAvailableAgents = async () => {
  loadingAgents.value = true
  try {
    const response = await aiApi.getAgents()
    if (response.data && response.data.agents) {
      availableAgents.value = response.data.agents
      message.success(`成功加载${response.data.total_agents}个AI Agent`)
    }
  } catch (error: any) {
    console.error('加载Agent列表失败:', error)
    message.error('加载Agent列表失败: ' + (error.message || '未知错误'))
  } finally {
    loadingAgents.value = false
  }
}

// 生命周期
onMounted(() => {
  // 加载Agent列表
  loadAvailableAgents()
  // TODO: 从后端加载当前设置
})
</script>

<style scoped lang="less">
.settings-page {
  padding: 16px;
  
  h1 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 24px;
  }
  
  :deep(.ant-tabs-card) {
    .ant-tabs-content {
      margin-top: -16px;
      
      .ant-tabs-tabpane {
        background: #fff;
        padding: 24px;
      }
    }
  }
}
</style>