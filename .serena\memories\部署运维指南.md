# CampusGuard部署运维指南

## 开发环境部署

### 1. 环境准备
```bash
# 安装Python 3.13.2
python --version  # 确认版本

# 安装Node.js 20.19+
node --version    # 确认版本

# 安装MySQL 8.0
mysql --version   # 确认版本
```

### 2. 后端部署步骤
```bash
# 克隆项目
git clone [repository_url]
cd campusguard/backend

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# venv\Scripts\activate    # Windows

# 安装依赖
pip install -r requirements.txt

# 配置环境变量
cp .env.example .env
# 编辑.env文件，配置数据库和API密钥

# 初始化数据库
python -c "from app.core.database import init_db; init_db()"

# 启动开发服务器
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### 3. 前端部署步骤
```bash
cd ../frontend

# 安装依赖
npm install

# 配置环境变量
cp .env.example .env.development
# 编辑环境变量文件

# 启动开发服务器
npm run dev
```

## 生产环境部署

### 1. 后端生产部署
```bash
# 使用Gunicorn + Uvicorn Worker
pip install gunicorn

# 启动命令
gunicorn app.main:app \
  -w 4 \
  -k uvicorn.workers.UvicornWorker \
  --bind 0.0.0.0:8000 \
  --access-logfile logs/access.log \
  --error-logfile logs/error.log
```

### 2. 前端生产构建
```bash
# 构建生产版本
npm run build

# 使用Nginx部署
# nginx配置示例
server {
    listen 80;
    server_name campusguard.example.com;
    
    root /var/www/campusguard/dist;
    index index.html;
    
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    location /api {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    location /ws {
        proxy_pass http://localhost:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
```

### 3. 使用Docker部署（可选）
```dockerfile
# 后端Dockerfile
FROM python:3.13.2-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]

# docker-compose.yml
version: '3.8'
services:
  backend:
    build: ./backend
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=mysql+pymysql://campusguard:password@db:3306/campusguard
    depends_on:
      - db
      
  frontend:
    build: ./frontend
    ports:
      - "80:80"
      
  db:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=rootpassword
      - MYSQL_DATABASE=campusguard
      - MYSQL_USER=campusguard
      - MYSQL_PASSWORD=password
    volumes:
      - mysql_data:/var/lib/mysql
      
volumes:
  mysql_data:
```

## 运维监控

### 1. 日志管理
- **应用日志**：使用loguru，自动轮转，保留30天
- **访问日志**：Nginx/Gunicorn访问日志
- **错误日志**：系统错误和异常追踪
- **日志位置**：./logs/目录

### 2. 性能监控
- **CPU/内存**：使用系统工具（top, htop）
- **数据库**：MySQL慢查询日志
- **API响应**：FastAPI内置metrics
- **前端性能**：浏览器开发者工具

### 3. 备份策略
- **数据库备份**：每日自动备份MySQL
- **配置备份**：Git版本控制
- **日志归档**：定期压缩归档旧日志

### 4. 故障处理
- **服务重启**：systemctl或supervisor管理
- **数据库连接**：连接池自动重连
- **WebSocket断线**：前端自动重连机制
- **API限流**：防止过载

## 安全加固

### 1. 基础安全
- 修改默认密码
- 限制数据库访问IP
- 使用HTTPS证书
- 配置防火墙规则

### 2. API安全
- 请求频率限制
- 输入验证和过滤
- SQL注入防护（ORM）
- XSS防护（前端）

### 3. 部署检查清单
- [ ] 环境变量配置正确
- [ ] 数据库连接正常
- [ ] DeepSeek API密钥有效
- [ ] 日志目录可写
- [ ] 静态文件服务正常
- [ ] WebSocket连接正常
- [ ] 定时任务配置（如有）