#!/usr/bin/env python3
"""
Database management script for CampusGuard
"""
import asyncio
import sys
import os
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from alembic.config import Config
from alembic import command
from loguru import logger

from app.core.database import engine, create_tables, init_db
from app.core.database_init import init_sample_data


def get_alembic_config():
    """Get Alembic configuration"""
    alembic_cfg = Config("alembic.ini")
    return alembic_cfg


def init_alembic():
    """Initialize Alembic migration environment"""
    try:
        logger.info("Initializing Alembic migration environment...")
        
        # Create alembic directory if it doesn't exist
        alembic_dir = Path("alembic")
        if not alembic_dir.exists():
            alembic_dir.mkdir()
            
        # Create versions directory
        versions_dir = alembic_dir / "versions"
        if not versions_dir.exists():
            versions_dir.mkdir()
            
        logger.info("Alembic environment initialized successfully")
        return True
        
    except Exception as e:
        logger.error(f"Failed to initialize Alembic: {e}")
        return False


def create_migration(message: str):
    """Create a new migration"""
    try:
        logger.info(f"Creating migration: {message}")
        alembic_cfg = get_alembic_config()
        command.revision(alembic_cfg, autogenerate=True, message=message)
        logger.info("Migration created successfully")
        return True
        
    except Exception as e:
        logger.error(f"Failed to create migration: {e}")
        return False


def upgrade_database(revision: str = "head"):
    """Upgrade database to specified revision"""
    try:
        logger.info(f"Upgrading database to revision: {revision}")
        alembic_cfg = get_alembic_config()
        command.upgrade(alembic_cfg, revision)
        logger.info("Database upgraded successfully")
        return True
        
    except Exception as e:
        logger.error(f"Failed to upgrade database: {e}")
        return False


def downgrade_database(revision: str):
    """Downgrade database to specified revision"""
    try:
        logger.info(f"Downgrading database to revision: {revision}")
        alembic_cfg = get_alembic_config()
        command.downgrade(alembic_cfg, revision)
        logger.info("Database downgraded successfully")
        return True
        
    except Exception as e:
        logger.error(f"Failed to downgrade database: {e}")
        return False


def show_current_revision():
    """Show current database revision"""
    try:
        alembic_cfg = get_alembic_config()
        command.current(alembic_cfg)
        return True
        
    except Exception as e:
        logger.error(f"Failed to show current revision: {e}")
        return False


def show_migration_history():
    """Show migration history"""
    try:
        alembic_cfg = get_alembic_config()
        command.history(alembic_cfg)
        return True
        
    except Exception as e:
        logger.error(f"Failed to show migration history: {e}")
        return False


def create_tables_direct():
    """Create tables directly without migrations (for development)"""
    try:
        logger.info("Creating database tables directly...")
        create_tables()
        logger.info("Database tables created successfully")
        return True
        
    except Exception as e:
        logger.error(f"Failed to create tables: {e}")
        return False


async def init_sample_data_async():
    """Initialize sample data"""
    try:
        logger.info("Initializing sample data...")
        await init_sample_data()
        logger.info("Sample data initialized successfully")
        return True
        
    except Exception as e:
        logger.error(f"Failed to initialize sample data: {e}")
        return False


def main():
    """Main function"""
    if len(sys.argv) < 2:
        print("Usage: python manage_db.py <command> [args]")
        print("Commands:")
        print("  init-alembic          - Initialize Alembic migration environment")
        print("  create-migration <msg> - Create a new migration")
        print("  upgrade [revision]     - Upgrade database (default: head)")
        print("  downgrade <revision>   - Downgrade database")
        print("  current               - Show current revision")
        print("  history               - Show migration history")
        print("  create-tables         - Create tables directly (development only)")
        print("  init-sample-data      - Initialize sample data")
        print("  full-setup            - Complete database setup (tables + sample data)")
        return
    
    command_name = sys.argv[1]
    
    if command_name == "init-alembic":
        init_alembic()
        
    elif command_name == "create-migration":
        if len(sys.argv) < 3:
            print("Error: Migration message required")
            return
        message = sys.argv[2]
        create_migration(message)
        
    elif command_name == "upgrade":
        revision = sys.argv[2] if len(sys.argv) > 2 else "head"
        upgrade_database(revision)
        
    elif command_name == "downgrade":
        if len(sys.argv) < 3:
            print("Error: Revision required")
            return
        revision = sys.argv[2]
        downgrade_database(revision)
        
    elif command_name == "current":
        show_current_revision()
        
    elif command_name == "history":
        show_migration_history()
        
    elif command_name == "create-tables":
        create_tables_direct()
        
    elif command_name == "init-sample-data":
        asyncio.run(init_sample_data_async())
        
    elif command_name == "full-setup":
        logger.info("Starting full database setup...")
        if create_tables_direct():
            asyncio.run(init_sample_data_async())
            logger.info("Full database setup completed")
        else:
            logger.error("Database setup failed")
            
    else:
        print(f"Unknown command: {command_name}")


if __name__ == "__main__":
    # Configure logging
    logger.remove()
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO"
    )
    
    main()