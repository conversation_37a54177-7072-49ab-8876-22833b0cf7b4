# CampusGuard项目依赖管理

## 前端依赖（package.json）

### 运行时依赖
```json
{
  "vue": "^3.5.17",                  // 核心框架
  "vue-router": "^4.5.1",            // 路由管理
  "pinia": "^3.0.3",                 // 状态管理
  "ant-design-vue": "^4.2.6",        // UI组件库
  "ant-design-x-vue": "^1.2.7",      // AI对话组件
  "@antv/g6": "^5.0.49",             // 网络拓扑图
  "axios": "^1.10.0",                // HTTP客户端
  "dayjs": "^1.11.13",               // 日期处理
  "echarts": "^5.6.0",               // 图表库
  "vue-echarts": "^7.0.3"            // Vue的ECharts包装器
}
```

### 开发依赖
```json
{
  "@vitejs/plugin-vue": "^6.0.0",    // Vite的Vue插件
  "vite": "^7.0.0",                  // 构建工具
  "eslint": "^9.30.0",               // 代码检查
  "eslint-plugin-vue": "^10.2.0",    // Vue的ESLint插件
  "prettier": "^3.6.2",              // 代码格式化
  "typescript": "^5.8.3"             // TypeScript支持
}
```

### 前端脚本命令
- `npm run dev`：启动开发服务器
- `npm run build`：构建生产版本
- `npm run preview`：预览生产构建
- `npm run lint`：ESLint代码检查
- `npm run format`：Prettier格式化

## 后端依赖（requirements.txt）

### Web框架
- fastapi==0.115.14 - 现代异步Web框架
- uvicorn[standard]==0.35.0 - ASGI服务器

### 数据库
- sqlalchemy==2.0.41 - ORM框架
- pymysql==1.1.1 - MySQL驱动
- alembic==1.16.2 - 数据库迁移工具（注意：已修正拼写）

### AI集成
- openai-agents[litellm]==0.1.0 - AI Agent框架，支持多种LLM

### 网络监控
- pysnmp==4.4.12 - SNMP协议实现
- python3-nmap==1.9.2 - 网络扫描工具

### 工具库
- loguru==0.7.3 - 日志管理
- python-dotenv==1.1.1 - 环境变量加载
- pydantic==2.11.7 - 数据验证
- pydantic-settings==2.10.1 - 配置管理
- websockets==15.0 - WebSocket支持
- httpx==0.28.1 - 异步HTTP客户端
- requests==2.32.4 - 同步HTTP客户端

## 版本管理策略

### 1. 版本锁定
- 所有生产依赖使用精确版本号（==）
- 避免使用范围版本号，确保构建一致性

### 2. 依赖更新流程
1. 定期检查安全更新
2. 在开发环境测试新版本
3. 运行完整测试套件
4. 更新requirements.txt/package.json
5. 提交版本变更

### 3. 依赖安装
```bash
# 后端
pip install -r requirements.txt

# 前端
npm install
```

## 依赖兼容性注意事项

### Python版本要求
- Python 3.13.2+
- 使用最新的异步特性

### Node.js版本要求
- Node.js 20.19+
- 支持ES模块

### 关键依赖说明
1. **FastAPI + Uvicorn**：高性能异步Web组合
2. **SQLAlchemy 2.0+**：支持异步操作的ORM
3. **Ant Design Vue 4.x**：Vue 3的企业级UI库
4. **openai-agents[litellm]**：统一的LLM接口，支持多种AI模型

## 依赖安全建议
1. 定期运行安全审计：
   - `npm audit` （前端）
   - `pip-audit` （后端）
2. 及时更新有安全漏洞的包
3. 使用依赖锁文件（package-lock.json）
4. 在CI/CD中集成依赖检查