<template>
  <div class="analysis-history">
    <a-page-header
      title="AI分析历史"
      sub-title="查看和管理AI分析结果"
      @back="() => $router.go(-1)"
    >
      <template #extra>
        <a-space>
          <a-button @click="refreshData" :loading="loading">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
          <a-button @click="exportAllResults">
            <template #icon><DownloadOutlined /></template>
            导出全部
          </a-button>
        </a-space>
      </template>
    </a-page-header>

    <!-- 筛选器 -->
    <a-card class="filter-card">
      <a-form layout="inline" :model="filters" @finish="loadAnalysisResults">
        <a-form-item label="分析类型">
          <a-select
            v-model:value="filters.analysis_type"
            placeholder="选择分析类型"
            style="width: 150px"
            allowClear
          >
            <a-select-option value="network_analysis">网络分析</a-select-option>
            <a-select-option value="security_analysis">安全分析</a-select-option>
            <a-select-option value="performance_analysis">性能分析</a-select-option>
            <a-select-option value="threat_detection">威胁检测</a-select-option>
            <a-select-option value="capacity_planning">容量规划</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="设备">
          <a-select
            v-model:value="filters.device_id"
            placeholder="选择设备"
            style="width: 200px"
            allowClear
            show-search
            :filter-option="filterDeviceOption"
          >
            <a-select-option
              v-for="device in devices"
              :key="device.id"
              :value="device.id"
            >
              {{ device.name }} ({{ device.ip_address }})
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="时间范围">
          <a-range-picker
            v-model:value="filters.dateRange"
            format="YYYY-MM-DD"
            style="width: 240px"
          />
        </a-form-item>

        <a-form-item>
          <a-button type="primary" html-type="submit" :loading="loading">
            <template #icon><SearchOutlined /></template>
            搜索
          </a-button>
        </a-form-item>

        <a-form-item>
          <a-button @click="resetFilters">
            <template #icon><ClearOutlined /></template>
            重置
          </a-button>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 分析结果列表 -->
    <a-card title="分析结果" class="results-card">
      <template #extra>
        <a-space>
          <span>共 {{ pagination.total }} 条记录</span>
          <a-select
            v-model:value="pagination.pageSize"
            style="width: 100px"
            @change="handlePageSizeChange"
          >
            <a-select-option :value="10">10条/页</a-select-option>
            <a-select-option :value="20">20条/页</a-select-option>
            <a-select-option :value="50">50条/页</a-select-option>
          </a-select>
        </a-space>
      </template>

      <a-table
        :columns="columns"
        :data-source="analysisResults"
        :loading="loading"
        :pagination="tablePagination"
        @change="handleTableChange"
        :scroll="{ x: 1200 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'analysis_type'">
            <a-tag :color="getAnalysisTypeColor(record.analysis_type)">
              {{ getAnalysisTypeName(record.analysis_type) }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'title'">
            <a-button type="link" @click="viewAnalysisDetail(record)">
              {{ record.title }}
            </a-button>
          </template>

          <template v-else-if="column.key === 'confidence_score'">
            <a-progress
              v-if="record.confidence_score !== null"
              :percent="Math.round(record.confidence_score * 100)"
              size="small"
              :stroke-color="getConfidenceColor(record.confidence_score)"
            />
            <span v-else>-</span>
          </template>

          <template v-else-if="column.key === 'processing_time'">
            <span v-if="record.processing_time">
              {{ formatProcessingTime(record.processing_time) }}
            </span>
            <span v-else>-</span>
          </template>

          <template v-else-if="column.key === 'analyzed_at'">
            {{ formatTimestamp(record.analyzed_at) }}
          </template>

          <template v-else-if="column.key === 'device'">
            <span v-if="record.device_id">
              {{ getDeviceName(record.device_id) }}
            </span>
            <span v-else>-</span>
          </template>

          <template v-else-if="column.key === 'actions'">
            <a-space>
              <a-button type="link" size="small" @click="viewAnalysisDetail(record)">
                查看详情
              </a-button>
              <a-button type="link" size="small" @click="exportSingleResult(record)">
                导出
              </a-button>
              <a-button type="link" size="small" @click="rerunAnalysis(record)">
                重新分析
              </a-button>
              <a-popconfirm
                title="确定要删除这个分析结果吗？"
                @confirm="deleteAnalysisResult(record.id)"
              >
                <a-button type="link" size="small" danger>
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 分析详情弹窗 -->
    <a-modal
      v-model:open="showDetailModal"
      title="分析详情"
      width="1000px"
      :footer="null"
    >
      <div v-if="selectedAnalysis" class="analysis-detail">
        <!-- 基本信息 -->
        <a-descriptions title="基本信息" :column="2" bordered>
          <a-descriptions-item label="分析类型">
            <a-tag :color="getAnalysisTypeColor(selectedAnalysis.analysis_type)">
              {{ getAnalysisTypeName(selectedAnalysis.analysis_type) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="分析时间">
            {{ formatTimestamp(selectedAnalysis.analyzed_at) }}
          </a-descriptions-item>
          <a-descriptions-item label="置信度">
            <a-progress
              v-if="selectedAnalysis.confidence_score !== null"
              :percent="Math.round(selectedAnalysis.confidence_score * 100)"
              size="small"
              :stroke-color="getConfidenceColor(selectedAnalysis.confidence_score)"
            />
            <span v-else>-</span>
          </a-descriptions-item>
          <a-descriptions-item label="处理时间">
            {{ formatProcessingTime(selectedAnalysis.processing_time) }}
          </a-descriptions-item>
          <a-descriptions-item label="使用模型">
            {{ selectedAnalysis.model_used || 'DeepSeek V3' }}
          </a-descriptions-item>
          <a-descriptions-item label="相关设备">
            {{ getDeviceName(selectedAnalysis.device_id) || '-' }}
          </a-descriptions-item>
        </a-descriptions>

        <!-- 分析摘要 -->
        <div v-if="selectedAnalysis.summary" class="analysis-section">
          <h4>分析摘要</h4>
          <a-typography-paragraph>
            {{ selectedAnalysis.summary }}
          </a-typography-paragraph>
        </div>

        <!-- 分析发现 -->
        <div v-if="selectedAnalysis.findings" class="analysis-section">
          <h4>分析发现</h4>
          <div class="findings-content">
            <pre>{{ JSON.stringify(selectedAnalysis.findings, null, 2) }}</pre>
          </div>
        </div>

        <!-- 建议措施 -->
        <div v-if="selectedAnalysis.recommendations" class="analysis-section">
          <h4>建议措施</h4>
          <div class="recommendations-content">
            <pre>{{ JSON.stringify(selectedAnalysis.recommendations, null, 2) }}</pre>
          </div>
        </div>

        <!-- 输入数据 -->
        <div v-if="selectedAnalysis.input_data" class="analysis-section">
          <h4>输入数据</h4>
          <a-collapse>
            <a-collapse-panel key="input" header="查看输入数据">
              <pre>{{ JSON.stringify(selectedAnalysis.input_data, null, 2) }}</pre>
            </a-collapse-panel>
          </a-collapse>
        </div>

        <!-- Token使用情况 -->
        <div v-if="selectedAnalysis.token_usage" class="analysis-section">
          <h4>Token使用情况</h4>
          <a-descriptions :column="3" size="small">
            <a-descriptions-item label="输入Token">
              {{ selectedAnalysis.token_usage.prompt_tokens || 0 }}
            </a-descriptions-item>
            <a-descriptions-item label="输出Token">
              {{ selectedAnalysis.token_usage.completion_tokens || 0 }}
            </a-descriptions-item>
            <a-descriptions-item label="总Token">
              {{ selectedAnalysis.token_usage.total_tokens || 0 }}
            </a-descriptions-item>
          </a-descriptions>
        </div>

        <!-- 操作按钮 -->
        <div class="analysis-actions">
          <a-space>
            <a-button type="primary" @click="exportSingleResult(selectedAnalysis)">
              <template #icon><DownloadOutlined /></template>
              导出结果
            </a-button>
            <a-button @click="rerunAnalysis(selectedAnalysis)">
              <template #icon><RedoOutlined /></template>
              重新分析
            </a-button>
            <a-button @click="showDetailModal = false">
              关闭
            </a-button>
          </a-space>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import dayjs, { type Dayjs } from 'dayjs'
import {
  ReloadOutlined,
  DownloadOutlined,
  SearchOutlined,
  ClearOutlined,
  RedoOutlined
} from '@ant-design/icons-vue'

import { aiApi, type AnalysisResponse } from '@/api/ai'
import { deviceApi, type Device } from '@/api/device'

// 响应式数据
const loading = ref(false)
const analysisResults = ref<AnalysisResponse[]>([])
const devices = ref<Device[]>([])
const showDetailModal = ref(false)
const selectedAnalysis = ref<AnalysisResponse | null>(null)

// 筛选器
const filters = ref({
  analysis_type: undefined as string | undefined,
  device_id: undefined as number | undefined,
  dateRange: undefined as [Dayjs, Dayjs] | undefined
})

// 分页
const pagination = ref({
  current: 1,
  pageSize: 20,
  total: 0
})

// 表格列定义
const columns = [
  { title: '标题', key: 'title', width: 200, ellipsis: true },
  { title: '分析类型', key: 'analysis_type', width: 120 },
  { title: '相关设备', key: 'device', width: 150 },
  { title: '置信度', key: 'confidence_score', width: 100 },
  { title: '处理时间', key: 'processing_time', width: 100 },
  { title: '分析时间', key: 'analyzed_at', width: 150 },
  { title: '操作', key: 'actions', width: 200, fixed: 'right' }
]

// 计算属性
const tablePagination = computed(() => ({
  current: pagination.value.current,
  pageSize: pagination.value.pageSize,
  total: pagination.value.total,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number, range: [number, number]) => 
    `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
}))

// 方法
const loadAnalysisResults = async () => {
  loading.value = true
  try {
    const params: any = {
      skip: (pagination.value.current - 1) * pagination.value.pageSize,
      limit: pagination.value.pageSize
    }

    if (filters.value.analysis_type) {
      params.analysis_type = filters.value.analysis_type
    }
    if (filters.value.device_id) {
      params.device_id = filters.value.device_id
    }
    if (filters.value.dateRange) {
      params.start_date = filters.value.dateRange[0].format('YYYY-MM-DD')
      params.end_date = filters.value.dateRange[1].format('YYYY-MM-DD')
    }

    const response = await aiApi.getAnalysisResults(params)
    analysisResults.value = response.data || []
    pagination.value.total = response.total || 0
  } catch (error: any) {
    message.error(`加载分析结果失败: ${error.message}`)
  } finally {
    loading.value = false
  }
}

const loadDevices = async () => {
  try {
    const response = await deviceApi.getDevices()
    devices.value = response.data || []
  } catch (error: any) {
    console.error('加载设备列表失败:', error)
  }
}

const refreshData = () => {
  loadAnalysisResults()
  loadDevices()
}

const resetFilters = () => {
  filters.value = {
    analysis_type: undefined,
    device_id: undefined,
    dateRange: undefined
  }
  pagination.value.current = 1
  loadAnalysisResults()
}

const handleTableChange = (pag: any) => {
  pagination.value.current = pag.current
  pagination.value.pageSize = pag.pageSize
  loadAnalysisResults()
}

const handlePageSizeChange = () => {
  pagination.value.current = 1
  loadAnalysisResults()
}

const viewAnalysisDetail = (record: AnalysisResponse) => {
  selectedAnalysis.value = record
  showDetailModal.value = true
}

const exportSingleResult = (record: AnalysisResponse) => {
  const content = `分析结果导出
标题: ${record.title}
分析类型: ${getAnalysisTypeName(record.analysis_type)}
分析时间: ${formatTimestamp(record.analyzed_at)}
置信度: ${record.confidence_score ? Math.round(record.confidence_score * 100) + '%' : '-'}

摘要:
${record.summary || '无'}

分析发现:
${record.findings ? JSON.stringify(record.findings, null, 2) : '无'}

建议措施:
${record.recommendations ? JSON.stringify(record.recommendations, null, 2) : '无'}
`

  const blob = new Blob([content], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `analysis-${record.id}-${dayjs().format('YYYY-MM-DD-HH-mm-ss')}.txt`
  a.click()
  URL.revokeObjectURL(url)
}

const exportAllResults = () => {
  if (analysisResults.value.length === 0) {
    message.warning('没有可导出的数据')
    return
  }

  const content = analysisResults.value.map(record => 
    `分析ID: ${record.id}
标题: ${record.title}
分析类型: ${getAnalysisTypeName(record.analysis_type)}
分析时间: ${formatTimestamp(record.analyzed_at)}
置信度: ${record.confidence_score ? Math.round(record.confidence_score * 100) + '%' : '-'}
摘要: ${record.summary || '无'}
---`
  ).join('\n\n')

  const blob = new Blob([content], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `analysis-results-${dayjs().format('YYYY-MM-DD-HH-mm-ss')}.txt`
  a.click()
  URL.revokeObjectURL(url)
}

const rerunAnalysis = async (record: AnalysisResponse) => {
  try {
    message.info('正在重新分析...')
    // 这里应该调用重新分析的API
    // await aiApi.rerunAnalysis(record.id)
    message.success('重新分析已启动')
  } catch (error: any) {
    message.error(`重新分析失败: ${error.message}`)
  }
}

const deleteAnalysisResult = async (id: number) => {
  try {
    // 这里应该调用删除API
    // await aiApi.deleteAnalysisResult(id)
    message.success('删除成功')
    loadAnalysisResults()
  } catch (error: any) {
    message.error(`删除失败: ${error.message}`)
  }
}

// 工具函数
const getAnalysisTypeName = (type: string) => {
  const typeMap = {
    network_analysis: '网络分析',
    security_analysis: '安全分析',
    performance_analysis: '性能分析',
    threat_detection: '威胁检测',
    capacity_planning: '容量规划'
  }
  return typeMap[type as keyof typeof typeMap] || type
}

const getAnalysisTypeColor = (type: string) => {
  const colorMap = {
    network_analysis: 'blue',
    security_analysis: 'red',
    performance_analysis: 'green',
    threat_detection: 'orange',
    capacity_planning: 'purple'
  }
  return colorMap[type as keyof typeof colorMap] || 'default'
}

const getConfidenceColor = (score: number) => {
  if (score >= 0.8) return '#52c41a'
  if (score >= 0.6) return '#faad14'
  if (score >= 0.4) return '#fa8c16'
  return '#ff4d4f'
}

const formatProcessingTime = (time?: number) => {
  if (!time) return '-'
  if (time < 1) return `${Math.round(time * 1000)}ms`
  return `${time.toFixed(2)}s`
}

const formatTimestamp = (timestamp: string) => {
  return dayjs(timestamp).format('YYYY-MM-DD HH:mm:ss')
}

const getDeviceName = (deviceId?: number) => {
  if (!deviceId) return '-'
  const device = devices.value.find(d => d.id === deviceId)
  return device ? `${device.name} (${device.ip_address})` : `设备 ${deviceId}`
}

const filterDeviceOption = (input: string, option: any) => {
  return option.children.toLowerCase().includes(input.toLowerCase())
}

// 生命周期
onMounted(() => {
  loadAnalysisResults()
  loadDevices()
})
</script>

<style scoped>
.analysis-history {
  padding: 24px;
}

.filter-card {
  margin-bottom: 16px;
}

.results-card {
  margin-bottom: 16px;
}

.analysis-detail {
  max-height: 70vh;
  overflow-y: auto;
}

.analysis-section {
  margin-bottom: 24px;
}

.analysis-section h4 {
  margin-bottom: 12px;
  font-size: 16px;
  font-weight: 500;
}

.findings-content,
.recommendations-content {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 6px;
  max-height: 200px;
  overflow-y: auto;
}

.findings-content pre,
.recommendations-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.analysis-actions {
  margin-top: 24px;
  text-align: center;
}
</style>
