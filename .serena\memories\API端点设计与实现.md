# CampusGuard API端点设计与实现

## API架构概览

### 路由结构
```
/api/v1/
├── /devices      # 设备管理
├── /alerts       # 告警管理  
├── /performance  # 性能监控
├── /ai           # AI对话分析
├── /ws           # WebSocket连接
├── /threat       # 威胁情报
└── /monitoring   # 监控控制
```

## AI端点（/api/v1/ai）

### 1. POST /chat - AI对话
```python
请求体:
{
    "message": "用户消息",
    "conversation_id": 1,  # 可选
    "agent_type": "general",  # general/security/performance
    "user_id": "user123",
    "session_id": "session456"
}

响应:
{
    "conversation_id": 1,
    "message": "AI回复内容",
    "timestamp": "2024-01-15T10:30:00"
}
```

### 2. POST /analyze - AI分析
```python
请求体:
{
    "analysis_type": "security_analysis",
    "query": "分析查询",
    "data": {...},
    "conversation_id": 1,
    "device_id": 1
}

响应: AnalysisResult对象
```

### 3. GET /agents - 获取可用Agent
```python
响应:
{
    "agents": {
        "general": {...},
        "security": {...},
        "performance": {...}
    },
    "total_agents": 3,
    "timestamp": "..."
}
```

### 4. POST /chat/{agent_type} - 指定Agent对话
- 动态路由，根据agent_type选择对应Agent
- 支持会话管理和消息持久化

## 设备端点（/api/v1/devices）

### 核心端点
- GET /devices - 获取设备列表
- GET /devices/{device_id} - 获取设备详情
- POST /devices - 创建设备
- PUT /devices/{device_id} - 更新设备
- DELETE /devices/{device_id} - 删除设备
- GET /devices/{device_id}/performance - 设备性能数据

### 查询参数
- status: 按状态筛选
- device_type: 按类型筛选
- location: 按位置筛选
- skip/limit: 分页参数

## 告警端点（/api/v1/alerts）

### 核心功能
- GET /alerts - 告警列表（支持筛选）
- POST /alerts/{alert_id}/acknowledge - 确认告警
- POST /alerts/{alert_id}/resolve - 解决告警
- GET /alerts/statistics - 告警统计
- GET /alerts/trends - 告警趋势

### 告警筛选
- severity: 按级别筛选
- status: 按状态筛选
- device_id: 按设备筛选
- time_range: 时间范围

## 性能端点（/api/v1/performance）

### 数据查询
- GET /performance/metrics - 性能指标
- GET /performance/overview - 性能概览
- GET /performance/device/{device_id} - 设备性能
- GET /performance/trends - 性能趋势

### 聚合分析
- GET /performance/top-issues - 性能问题排行
- GET /performance/capacity - 容量分析
- POST /performance/analyze - 性能分析

## WebSocket端点（/api/v1/ws）

### 连接管理
- WS /ws/connect - WebSocket连接
- GET /ws/status - 连接状态
- POST /ws/broadcast - 广播消息

### 消息类型
```javascript
// 设备更新
{
    "type": "device_update",
    "data": {...},
    "timestamp": "..."
}

// 新告警
{
    "type": "new_alert",
    "data": {...},
    "timestamp": "..."
}

// 性能数据
{
    "type": "performance_update",
    "data": {...},
    "timestamp": "..."
}
```

## 威胁情报端点（/api/v1/threat）

### IP信誉
- GET /threat/ip/{ip_address} - 检查IP信誉
- GET /threat/blacklist - 获取黑名单
- POST /threat/blacklist - 添加到黑名单
- DELETE /threat/blacklist/{ip} - 从黑名单移除

### 威胁分析
- GET /threat/statistics - 威胁统计
- GET /threat/events - 威胁事件
- POST /threat/analyze - 威胁分析

## 监控控制端点（/api/v1/monitoring）

### 监控管理
- POST /monitoring/start - 启动监控
- POST /monitoring/stop - 停止监控
- GET /monitoring/status - 监控状态
- PUT /monitoring/config - 更新配置

### 设备发现
- POST /monitoring/discover - 发现设备
- GET /monitoring/discovery-status - 发现状态

## API设计模式

### 1. 依赖注入
```python
async def endpoint(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
```

### 2. 响应模型
- 使用Pydantic Schema定义
- 自动序列化和验证
- 清晰的API文档

### 3. 错误处理
```python
if not resource:
    raise HTTPException(
        status_code=404,
        detail="Resource not found"
    )
```

### 4. 查询优化
- 使用ORM关系预加载
- 分页查询限制
- 索引优化

## 认证与授权

### 当前简化设计
- 无复杂认证系统
- 基于session_id的会话管理
- 适合内网部署

### 扩展建议
- JWT Token认证
- 角色权限控制
- API密钥管理

## API版本管理

### 版本策略
- URL路径版本：/api/v1/
- 向后兼容原则
- 弃用通知机制

### 版本迁移
```python
# v1保持稳定
/api/v1/devices

# v2新增功能
/api/v2/devices  # 未来扩展
```

## 性能优化

### 1. 数据库查询
- 使用select_related减少查询
- 限制返回字段
- 添加appropriate索引

### 2. 缓存策略
- 静态数据缓存
- 查询结果缓存
- WebSocket连接池

### 3. 异步处理
- 所有端点异步化
- 后台任务队列
- 非阻塞I/O操作

## API文档

### 自动文档生成
- FastAPI自动生成OpenAPI规范
- Swagger UI：/api/v1/docs
- ReDoc：/api/v1/redoc

### 文档增强
- 详细的端点描述
- 请求/响应示例
- 错误码说明

## 最佳实践

1. **RESTful设计**：遵循REST原则
2. **一致性**：统一的响应格式和错误处理
3. **版本控制**：清晰的版本策略
4. **文档完善**：自动生成和人工增强
5. **性能优先**：异步和缓存优化
6. **安全考虑**：输入验证和错误处理