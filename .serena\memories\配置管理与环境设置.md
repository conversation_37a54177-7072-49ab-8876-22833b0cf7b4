# CampusGuard配置管理与环境设置

## 配置架构

### 1. 技术栈
- **pydantic-settings 2.10.1**：类型安全的配置管理
- **python-dotenv 1.1.1**：环境变量加载
- **BaseSettings**：配置类基类

### 2. 配置文件位置
```
config/
├── .env.example    # 环境变量示例
└── .env           # 实际环境变量（不纳入版本控制）
```

## 核心配置类（Settings）

### 1. 应用配置
```python
# 基础设置
PROJECT_NAME: str = "CampusGuard"
API_V1_STR: str = "/api/v1"
DEBUG: bool = False

# 服务器配置
HOST: str = "0.0.0.0"
PORT: int = 8000
WORKERS: int = 1
```

### 2. 安全配置
```python
SECRET_KEY: str = "campusguard-secret-key-2024"
ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
REFRESH_TOKEN_EXPIRE_MINUTES: int = 10080  # 7天
ALGORITHM: str = "HS256"
```

### 3. 数据库配置
```python
# 完整连接字符串
DATABASE_URL: str = "mysql+pymysql://campusguard:campusguard123@localhost:3306/campusguard"

# 分离配置（便于动态构建）
DATABASE_HOST: str = "localhost"
DATABASE_PORT: int = 3306
DATABASE_NAME: str = "campusguard"
DATABASE_USER: str = "campusguard"
DATABASE_PASSWORD: str = "campusguard123"
```

### 4. DeepSeek AI配置
```python
DEEPSEEK_API_KEY: str = ""  # 通过环境变量设置
DEEPSEEK_BASE_URL: str = "https://api.deepseek.com"
DEEPSEEK_MODEL: str = "deepseek-chat"
```

### 5. CORS配置
```python
BACKEND_CORS_ORIGINS: List[str] = [
    "http://localhost:3000",     # React默认端口
    "http://localhost:5173",     # Vite默认端口
    "http://localhost:8080"      # 备用端口
]

# 智能解析：支持逗号分隔的字符串
@field_validator("BACKEND_CORS_ORIGINS", mode="before")
def assemble_cors_origins(cls, v):
    if isinstance(v, str) and not v.startswith("["):
        return [i.strip() for i in v.split(",")]
    return v
```

### 6. 网络监控配置
```python
SNMP_COMMUNITY: str = "public"    # SNMP团体字符串
SNMP_TIMEOUT: int = 5            # 超时秒数
SNMP_RETRIES: int = 3            # 重试次数
SNMP_VERSION: str = "2c"         # SNMP版本
```

### 7. WebSocket配置
```python
WS_HOST: str = "0.0.0.0"
WS_PORT: int = 8001
```

### 8. 日志配置
```python
LOG_LEVEL: str = "INFO"
LOG_FILE_PATH: str = "logs/campusguard.log"
LOG_ROTATION: str = "1 week"      # 日志轮转周期
LOG_RETENTION: str = "30 days"    # 日志保留时间
LOG_COMPRESSION: str = "zip"      # 压缩格式
```

## 环境变量管理

### 1. .env文件格式
```bash
# 数据库配置
DATABASE_URL=mysql+pymysql://username:password@localhost:3306/campusguard

# DeepSeek API配置
DEEPSEEK_API_KEY=sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
DEEPSEEK_BASE_URL=https://api.deepseek.com

# 应用配置
DEBUG=True
LOG_LEVEL=INFO
SECRET_KEY=your-secret-key-here

# 网络配置
SNMP_COMMUNITY=public
SNMP_TIMEOUT=5
SNMP_RETRIES=3
```

### 2. 环境变量优先级
1. 系统环境变量
2. .env文件
3. 默认值

### 3. 敏感信息保护
- API密钥不硬编码
- .env文件加入.gitignore
- 使用.env.example作为模板

## 日志配置（logging_config.py）

### 1. Loguru配置特点
```python
logger.add(
    settings.LOG_FILE_PATH,
    rotation=settings.LOG_ROTATION,
    retention=settings.LOG_RETENTION,
    compression=settings.LOG_COMPRESSION,
    level=settings.LOG_LEVEL,
    format="{time} | {level} | {message}"
)
```

### 2. 日志级别
- DEBUG：详细调试信息
- INFO：一般信息
- WARNING：警告信息
- ERROR：错误信息
- CRITICAL：严重错误

### 3. 结构化日志
```python
logger.info("设备状态更新", 
    device_id=device.id,
    status=device.status,
    ip=device.ip_address
)
```

## 数据库初始化（database_init.py）

### 1. 自动建表
```python
def init_db():
    """初始化数据库，创建所有表"""
    from .base import Base
    from ..models import *  # 导入所有模型
    
    Base.metadata.create_all(bind=engine)
```

### 2. 连接池配置
```python
engine = create_engine(
    settings.DATABASE_URL,
    pool_pre_ping=True,      # 连接健康检查
    pool_size=10,            # 连接池大小
    max_overflow=20          # 最大溢出连接数
)
```

### 3. 会话管理
```python
SessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine
)

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
```

## 配置加载流程

### 1. 应用启动时
```python
# main.py
from .core.config import settings

# settings实例自动加载配置
app = FastAPI(
    title=settings.PROJECT_NAME,
    debug=settings.DEBUG
)
```

### 2. 配置验证
- Pydantic自动类型验证
- 必填字段检查
- 默认值处理

### 3. 动态配置更新
```python
# 支持运行时更新（需要重启）
os.environ["LOG_LEVEL"] = "DEBUG"
```

## 多环境支持

### 1. 开发环境
```bash
# .env.development
DEBUG=True
LOG_LEVEL=DEBUG
DATABASE_URL=mysql+pymysql://dev:dev@localhost:3306/campusguard_dev
```

### 2. 生产环境
```bash
# .env.production
DEBUG=False
LOG_LEVEL=WARNING
DATABASE_URL=mysql+pymysql://prod:prod@prod-db:3306/campusguard
```

### 3. 环境切换
```python
# 通过环境变量控制
export ENVIRONMENT=production
```

## 配置最佳实践

### 1. 安全原则
- 敏感信息使用环境变量
- 不同环境使用不同密钥
- 定期轮换密钥

### 2. 可维护性
- 配置项分组管理
- 使用描述性名称
- 提供默认值和示例

### 3. 类型安全
- 使用Pydantic类型注解
- 自定义验证器
- 明确的错误提示

### 4. 文档化
- 每个配置项添加注释
- 维护.env.example
- README中说明配置方法

## 扩展配置

### 1. 缓存配置（未来）
```python
REDIS_URL: str = "redis://localhost:6379/0"
CACHE_TTL: int = 300  # 5分钟
```

### 2. 消息队列（未来）
```python
RABBITMQ_URL: str = "amqp://guest:guest@localhost:5672/"
QUEUE_NAME: str = "campusguard"
```

### 3. 监控配置（未来）
```python
PROMETHEUS_ENABLED: bool = True
METRICS_PORT: int = 9090
```

## 配置调试

### 1. 查看当前配置
```python
# 调试端点
@app.get("/debug/config")
async def get_config():
    if not settings.DEBUG:
        raise HTTPException(403)
    return {
        "project_name": settings.PROJECT_NAME,
        "debug": settings.DEBUG,
        "log_level": settings.LOG_LEVEL
    }
```

### 2. 配置验证工具
```python
# 启动时验证
python -c "from app.core.config import settings; print(settings.dict())"
```

这种配置管理方案确保了应用的灵活性、安全性和可维护性，便于在不同环境中部署和运行。