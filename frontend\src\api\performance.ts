import request from './request'

export interface PerformanceMetricsParams {
  device_id?: number
  metric_name?: string
  hours?: number
}

export interface PerformanceMetric {
  id: number
  device_id: number
  device_ip: string
  metric_name: string
  metric_value: number
  metric_unit: string
  timestamp: string
  metadata?: Record<string, any>
  created_at: string
  updated_at: string
  is_active: boolean
  // 扩展字段用于性能监控页面
  cpu_usage?: number
  memory_usage?: number
  disk_usage?: number
  network_in?: number
  network_out?: number
  response_time?: number
}

export interface PerformanceDataParams {
  device_id: number
  time_range?: string
  metric_types?: string
  start_time?: string
  end_time?: string
}

export interface NetworkTopology {
  nodes: TopologyNode[]
  edges: TopologyEdge[]
}

export interface TopologyNode {
  id: string
  type: string
  name: string
  ip: string
  x: number
  y: number
  properties: Record<string, any>
  style: Record<string, any>
}

export interface TopologyEdge {
  source: string
  target: string
  type: string
  bandwidth: number
  latency: number
  utilization: number
  status: string
  properties: Record<string, any>
  style: Record<string, any>
}

export interface DashboardData {
  timestamp: string
  device_metrics: Record<string, Record<string, number>>
  total_devices: number
}

export const performanceApi = {
  // Get performance data for monitoring page
  getPerformanceData(params: PerformanceDataParams) {
    return request.get<{
      data: PerformanceMetric[]
      total: number
      device_info?: any
    }>('/performance/data', { params })
  },

  // Get network topology
  getTopology() {
    return request.get<NetworkTopology>('/performance/topology')
  },

  // Get dashboard data
  getDashboard() {
    return request.get<DashboardData>('/performance/dashboard')
  }
}
