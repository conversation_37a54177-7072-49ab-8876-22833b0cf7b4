"""
Performance monitoring endpoints
"""
from typing import List, Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from loguru import logger

from ....core.database import get_db
from ....models.performance import PerformanceMetric, NetworkTopology, NetworkConnection
from ....models.device import Device

router = APIRouter()


@router.get("/metrics")
async def get_performance_metrics(
    device_id: Optional[int] = None,
    metric_name: Optional[str] = None,
    hours: int = Query(24, ge=1, le=168),  # Last 24 hours by default, max 1 week
    db: Session = Depends(get_db)
):
    """Get performance metrics"""
    try:
        # Calculate time range
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=hours)
        
        query = db.query(PerformanceMetric).filter(
            PerformanceMetric.is_active == True,
            PerformanceMetric.timestamp >= start_time.isoformat(),
            PerformanceMetric.timestamp <= end_time.isoformat()
        )
        
        if device_id:
            query = query.filter(PerformanceMetric.device_id == device_id)
        if metric_name:
            query = query.filter(PerformanceMetric.metric_name == metric_name)
            
        metrics = query.order_by(PerformanceMetric.timestamp.desc()).limit(1000).all()
        return metrics
        
    except Exception as e:
        logger.error(f"Error getting performance metrics: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve performance metrics")


@router.get("/topology")
async def get_network_topology(db: Session = Depends(get_db)):
    """Get network topology data"""
    try:
        # Get nodes
        nodes = db.query(NetworkTopology).filter(NetworkTopology.is_active == True).all()
        
        # Get connections
        connections = db.query(NetworkConnection).filter(NetworkConnection.is_active == True).all()
        
        return {
            "nodes": [
                {
                    "id": node.node_id,
                    "type": node.node_type,
                    "name": node.node_name,
                    "ip": node.node_ip,
                    "x": node.x_position,
                    "y": node.y_position,
                    "properties": node.properties,
                    "style": node.style
                }
                for node in nodes
            ],
            "edges": [
                {
                    "source": conn.source_node_id,
                    "target": conn.target_node_id,
                    "type": conn.connection_type,
                    "bandwidth": conn.bandwidth,
                    "latency": conn.latency,
                    "utilization": conn.utilization,
                    "status": conn.status,
                    "properties": conn.properties,
                    "style": conn.style
                }
                for conn in connections
            ]
        }
        
    except Exception as e:
        logger.error(f"Error getting network topology: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve network topology")


@router.get("/dashboard")
async def get_dashboard_data(db: Session = Depends(get_db)):
    """Get dashboard performance data"""
    try:
        # Get recent metrics for dashboard
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=1)  # Last hour
        
        # Get latest metrics per device
        latest_metrics = db.query(PerformanceMetric).filter(
            PerformanceMetric.is_active == True,
            PerformanceMetric.timestamp >= start_time.isoformat()
        ).all()
        
        # Group by device and metric type
        device_metrics = {}
        for metric in latest_metrics:
            device_id = metric.device_id
            if device_id not in device_metrics:
                device_metrics[device_id] = {}
            device_metrics[device_id][metric.metric_name] = metric.metric_value
        
        return {
            "timestamp": end_time.isoformat(),
            "device_metrics": device_metrics,
            "total_devices": len(device_metrics)
        }
        
    except Exception as e:
        logger.error(f"Error getting dashboard data: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve dashboard data")


@router.get("/data")
async def get_performance_data(
    device_id: int,
    time_range: Optional[str] = "24h",
    metric_types: Optional[str] = None,
    start_time: Optional[str] = None,
    end_time: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """Get performance data for monitoring page"""
    try:
        # Parse time range
        if start_time and end_time:
            start_dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
            end_dt = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
        else:
            # Parse time_range parameter
            end_dt = datetime.now()
            if time_range == "1h":
                start_dt = end_dt - timedelta(hours=1)
            elif time_range == "6h":
                start_dt = end_dt - timedelta(hours=6)
            elif time_range == "24h":
                start_dt = end_dt - timedelta(hours=24)
            elif time_range == "7d":
                start_dt = end_dt - timedelta(days=7)
            elif time_range == "30d":
                start_dt = end_dt - timedelta(days=30)
            else:
                start_dt = end_dt - timedelta(hours=24)  # Default to 24h

        # Get device info
        device = db.query(Device).filter(
            Device.id == device_id,
            Device.is_active == True
        ).first()
        
        if not device:
            raise HTTPException(status_code=404, detail="Device not found")

        # Build query for performance metrics
        query = db.query(PerformanceMetric).filter(
            PerformanceMetric.device_id == device_id,
            PerformanceMetric.is_active == True,
            PerformanceMetric.timestamp >= start_dt.isoformat(),
            PerformanceMetric.timestamp <= end_dt.isoformat()
        )

        # Filter by metric types if specified
        if metric_types:
            metric_type_list = [mt.strip() for mt in metric_types.split(',')]
            metric_names = []
            for mt in metric_type_list:
                if mt == "cpu":
                    metric_names.extend(["cpu_usage", "cpu_load"])
                elif mt == "memory":
                    metric_names.extend(["memory_usage", "memory_total", "memory_free"])
                elif mt == "network":
                    metric_names.extend(["network_in", "network_out", "network_packets_in", "network_packets_out"])
                elif mt == "disk":
                    metric_names.extend(["disk_usage", "disk_total", "disk_free", "disk_io_read", "disk_io_write"])
            
            if metric_names:
                query = query.filter(PerformanceMetric.metric_name.in_(metric_names))

        # Get metrics ordered by timestamp
        metrics = query.order_by(PerformanceMetric.timestamp.desc()).limit(1000).all()
        
        # Transform metrics to match frontend expectations
        performance_data = []
        
        # Group metrics by timestamp to create consolidated records
        metrics_by_timestamp = {}
        for metric in metrics:
            ts = metric.timestamp
            if ts not in metrics_by_timestamp:
                metrics_by_timestamp[ts] = {
                    'id': metric.id,
                    'device_id': metric.device_id,
                    'device_ip': metric.device_ip,
                    'timestamp': ts,
                    'created_at': metric.created_at.isoformat() if metric.created_at else ts,
                    'updated_at': metric.updated_at.isoformat() if metric.updated_at else ts,
                    'is_active': metric.is_active,
                    'metadata': metric.metadata or {}
                }
            
            # Map metric names to expected fields
            record = metrics_by_timestamp[ts]
            if metric.metric_name == "cpu_usage":
                record['cpu_usage'] = metric.metric_value
            elif metric.metric_name == "memory_usage":
                record['memory_usage'] = metric.metric_value
            elif metric.metric_name == "disk_usage":
                record['disk_usage'] = metric.metric_value
            elif metric.metric_name == "network_in":
                record['network_in'] = metric.metric_value
            elif metric.metric_name == "network_out":
                record['network_out'] = metric.metric_value
            elif metric.metric_name == "response_time":
                record['response_time'] = metric.metric_value
            
            # Store original metric info
            record['metric_name'] = metric.metric_name
            record['metric_value'] = metric.metric_value
            record['metric_unit'] = metric.metric_unit

        # Convert to list and sort by timestamp
        performance_data = list(metrics_by_timestamp.values())
        performance_data.sort(key=lambda x: x['timestamp'], reverse=True)

        return {
            "data": performance_data,
            "total": len(performance_data),
            "device_info": {
                "id": device.id,
                "name": device.name,
                "ip_address": device.ip_address,
                "device_type": device.device_type,
                "status": device.status,
                "location": device.location
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting performance data: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve performance data")
