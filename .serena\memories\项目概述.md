# CampusGuard项目概述

## 项目定位
CampusGuard是一个基于AI的简化版网络安全监控平台，专为校园网络环境设计，提供基础监控、威胁检测和智能分析功能。

## 核心特点
- **简化架构设计**：降低部署和维护复杂度
- **AI智能分析**：基于OpenAI Agents框架 + DeepSeek V3模型
- **实时监控**：网络设备状态监控和威胁检测
- **可视化展示**：Vue.js + G6网络拓扑图 + ECharts

## 技术栈
- **后端**：Python 3.13.2 + FastAPI + MySQL + OpenAI Agents + DeepSeek V3
- **前端**：Vue.js 3.5.17 + TypeScript + ant-design-vue + ant-design-x-vue + @antv/g6 + echarts
- **数据库**：MySQL 8.0 + SQLAlchemy 2.0+
- **日志**：loguru 0.7.3
- **构建工具**：Vite 7.0.0

## 项目规模
- 文档：2751行完整PRD文档
- 目标：50-100台设备监控
- 开发周期：10-12周（简化后）
- 架构层次：8层基础架构