# CampusGuard服务层与业务逻辑实现

## 服务层架构

### 设计原则
1. **单一职责**：每个服务专注一个业务领域
2. **依赖注入**：通过构造函数注入依赖
3. **异步优先**：所有I/O操作异步化
4. **错误处理**：统一的异常处理策略

### 核心服务
```
services/
├── alert_service.py         # 告警服务
├── device_service.py        # 设备管理服务
├── monitoring_service.py    # 监控服务
├── snmp_service.py         # SNMP采集服务
├── threat_intelligence.py   # 威胁情报服务
└── websocket_service.py    # WebSocket广播服务
```

## WebSocket服务实现

### 1. 广播服务架构
```python
class WebSocketService:
    def __init__(self):
        self.broadcast_queue = asyncio.Queue()
        self.is_broadcasting = False
```

### 2. 异步广播循环
```python
async def _broadcasting_loop(self):
    while self.is_broadcasting:
        try:
            message = await asyncio.wait_for(
                self.broadcast_queue.get(), 
                timeout=1.0
            )
            await manager.broadcast(json.dumps(message))
        except asyncio.TimeoutError:
            continue
```

### 3. 消息类型处理
- device_update：设备状态变更
- new_alert：新告警通知
- performance_update：性能数据更新
- topology_update：拓扑结构变化
- ai_response：AI响应消息

### 4. 用户定向发送
```python
async def send_to_user(self, message: str, user_id: str):
    # 支持向特定用户发送消息
    # 用于AI对话等个性化场景
```

## 监控服务（MonitoringService）

### 1. 生命周期管理
```python
async def start_monitoring(self):
    # 启动定期监控任务
    self.monitoring_task = asyncio.create_task(
        self._monitoring_loop()
    )

async def stop_monitoring(self):
    # 优雅停止监控
    self.is_monitoring = False
    await self.monitoring_task
```

### 2. 监控循环设计
```python
async def _monitoring_loop(self):
    while self.is_monitoring:
        # 1. 设备状态检查
        # 2. 性能数据采集
        # 3. 告警规则评估
        # 4. 广播状态更新
        await asyncio.sleep(self.interval)
```

### 3. 设备发现功能
- 网络扫描
- SNMP自动发现
- 设备类型识别
- 自动注册入库

## SNMP服务（SNMPService）

### 1. SNMP采集配置
```python
配置参数:
- community: 团体字符串
- version: SNMP版本(2c/3)
- timeout: 超时时间
- retries: 重试次数
```

### 2. OID映射
```python
OIDS = {
    'sysDescr': '*******.*******.0',
    'sysUpTime': '*******.*******.0',
    'ifInOctets': '*******.*******.1.10',
    'ifOutOctets': '*******.*******.1.16'
}
```

### 3. 批量采集优化
- 异步并发采集
- 连接池管理
- 失败重试机制
- 结果缓存

## 威胁情报服务

### 1. IP信誉检查
```python
async def check_ip_reputation(self, ip_address: str):
    # 内置规则检查
    # 黑名单匹配
    # 威胁评分计算
    return {
        "is_malicious": bool,
        "threat_level": str,
        "confidence": float,
        "reason": str
    }
```

### 2. 内置威胁规则
```python
MALICIOUS_PATTERNS = {
    "scanners": ["45.143.203.", "185.191.126."],
    "botnets": ["192.42.116.", "185.220."],
    "tor_exits": ["185.220.100.", "107.189."]
}
```

### 3. 威胁统计分析
- 威胁趋势分析
- 攻击来源分布
- 威胁类型统计
- 实时威胁评分

## 告警服务（AlertService）

### 1. 告警生成流程
```python
async def create_alert(self, alert_data):
    # 1. 验证告警数据
    # 2. 检查重复告警
    # 3. 应用告警规则
    # 4. 创建告警记录
    # 5. 触发通知
    # 6. 广播告警事件
```

### 2. 告警去重机制
```python
def is_duplicate_alert(self, new_alert):
    # 基于设备+类型+时间窗口
    # 避免告警风暴
    # 合并相似告警
```

### 3. 告警升级策略
- 基于持续时间升级
- 基于影响范围升级
- 基于业务重要性升级

### 4. 告警通知
- WebSocket实时推送
- 邮件通知（扩展）
- 短信通知（扩展）
- Webhook集成（扩展）

## 设备服务（DeviceService）

### 1. 设备生命周期
```python
设备状态流转:
UNKNOWN -> ONLINE -> WARNING -> CRITICAL -> OFFLINE
```

### 2. 健康度计算
```python
def calculate_health_score(device):
    # CPU使用率权重：30%
    # 内存使用率权重：30%
    # 磁盘使用率权重：20%
    # 网络状态权重：20%
    return weighted_score
```

### 3. 设备分组管理
- 按类型分组
- 按位置分组
- 按重要性分组
- 自定义标签分组

## 性能数据服务

### 1. 数据采集策略
```python
采集间隔:
- 关键设备：1分钟
- 普通设备：5分钟
- 低优先级：15分钟
```

### 2. 数据聚合
```python
async def aggregate_metrics(self, time_range):
    # 分钟级聚合
    # 小时级聚合
    # 天级聚合
    # 趋势计算
```

### 3. 性能基线
- 自动学习基线
- 异常检测算法
- 动态阈值调整

## 服务间通信

### 1. 事件驱动架构
```python
# 事件发布
await event_bus.publish("device.status.changed", device_data)

# 事件订阅
@event_bus.subscribe("device.status.changed")
async def handle_device_change(data):
    # 处理设备状态变更
```

### 2. 服务依赖管理
```python
class ServiceRegistry:
    _services = {}
    
    @classmethod
    def register(cls, name, service):
        cls._services[name] = service
    
    @classmethod
    def get(cls, name):
        return cls._services.get(name)
```

## 错误处理策略

### 1. 分层错误处理
```python
try:
    # 业务逻辑
except ValidationError:
    # 数据验证错误
except DatabaseError:
    # 数据库错误
except NetworkError:
    # 网络错误
except Exception as e:
    # 未知错误
    logger.error(f"Unexpected error: {e}")
```

### 2. 错误恢复机制
- 自动重试
- 降级处理
- 熔断机制
- 错误上报

## 性能优化

### 1. 异步并发
- 使用asyncio.gather并发执行
- 连接池复用
- 批量操作优化

### 2. 缓存策略
- 内存缓存（设备状态）
- 结果缓存（性能数据）
- 查询缓存（威胁情报）

### 3. 资源管理
- 连接池大小控制
- 内存使用监控
- 任务队列管理

## 扩展性设计

### 1. 插件化架构
```python
class ServicePlugin:
    def on_device_update(self, device): pass
    def on_alert_created(self, alert): pass
    def on_metric_received(self, metric): pass
```

### 2. 配置驱动
- 动态加载配置
- 热更新支持
- 环境隔离

### 3. 水平扩展
- 无状态服务设计
- 分布式任务调度
- 负载均衡支持