#!/bin/bash

# CampusGuard 生产环境数据库初始化脚本
# 使用方法: ./init_production_db.sh [database_name] [username] [password]

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 参数设置
DB_NAME=${1:-"campusguard"}
DB_USER=${2:-"campusguard"}
DB_PASS=${3:-""}
MYSQL_ROOT_PASS=""

# 检查参数
if [ -z "$DB_PASS" ]; then
    log_error "数据库密码不能为空"
    echo "使用方法: $0 [database_name] [username] [password]"
    exit 1
fi

# 获取MySQL root密码
echo -n "请输入MySQL root密码: "
read -s MYSQL_ROOT_PASS
echo

if [ -z "$MYSQL_ROOT_PASS" ]; then
    log_error "MySQL root密码不能为空"
    exit 1
fi

log_info "开始初始化CampusGuard生产环境数据库..."
log_info "数据库名: $DB_NAME"
log_info "用户名: $DB_USER"

# 检查MySQL服务状态
log_info "检查MySQL服务状态..."
if ! systemctl is-active --quiet mysql; then
    log_error "MySQL服务未运行，请先启动MySQL服务"
    exit 1
fi
log_success "MySQL服务正在运行"

# 测试MySQL连接
log_info "测试MySQL连接..."
if ! mysql -u root -p"$MYSQL_ROOT_PASS" -e "SELECT 1;" > /dev/null 2>&1; then
    log_error "无法连接到MySQL，请检查root密码"
    exit 1
fi
log_success "MySQL连接测试成功"

# 检查数据库是否已存在
log_info "检查数据库是否已存在..."
if mysql -u root -p"$MYSQL_ROOT_PASS" -e "USE $DB_NAME;" > /dev/null 2>&1; then
    log_warning "数据库 $DB_NAME 已存在"
    echo -n "是否要删除并重新创建? (y/N): "
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        log_info "删除现有数据库..."
        mysql -u root -p"$MYSQL_ROOT_PASS" -e "DROP DATABASE $DB_NAME;"
        log_success "数据库已删除"
    else
        log_info "保留现有数据库，跳过创建步骤"
        DB_EXISTS=true
    fi
fi

# 创建数据库
if [ "$DB_EXISTS" != true ]; then
    log_info "创建数据库 $DB_NAME..."
    mysql -u root -p"$MYSQL_ROOT_PASS" -e "
        CREATE DATABASE $DB_NAME 
        CHARACTER SET utf8mb4 
        COLLATE utf8mb4_unicode_ci;
    "
    log_success "数据库创建成功"
fi

# 检查用户是否已存在
log_info "检查用户是否已存在..."
USER_EXISTS=$(mysql -u root -p"$MYSQL_ROOT_PASS" -e "SELECT COUNT(*) FROM mysql.user WHERE user='$DB_USER' AND host='localhost';" -s -N)

if [ "$USER_EXISTS" -gt 0 ]; then
    log_warning "用户 $DB_USER 已存在"
    echo -n "是否要删除并重新创建? (y/N): "
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        log_info "删除现有用户..."
        mysql -u root -p"$MYSQL_ROOT_PASS" -e "DROP USER '$DB_USER'@'localhost';"
        log_success "用户已删除"
        USER_EXISTS=0
    fi
fi

# 创建用户并授权
if [ "$USER_EXISTS" -eq 0 ]; then
    log_info "创建用户 $DB_USER 并授权..."
    mysql -u root -p"$MYSQL_ROOT_PASS" -e "
        CREATE USER '$DB_USER'@'localhost' IDENTIFIED BY '$DB_PASS';
        GRANT SELECT, INSERT, UPDATE, DELETE, CREATE, DROP, INDEX, ALTER 
        ON $DB_NAME.* TO '$DB_USER'@'localhost';
        FLUSH PRIVILEGES;
    "
    log_success "用户创建和授权成功"
fi

# 测试新用户连接
log_info "测试新用户连接..."
if ! mysql -u "$DB_USER" -p"$DB_PASS" -e "USE $DB_NAME;" > /dev/null 2>&1; then
    log_error "新用户连接测试失败"
    exit 1
fi
log_success "新用户连接测试成功"

# 创建环境配置文件
log_info "创建环境配置文件..."
ENV_FILE="config/.env.production"
mkdir -p config

cat > "$ENV_FILE" << EOF
# CampusGuard 生产环境配置
# 生成时间: $(date)

# 数据库配置
DATABASE_URL=mysql+pymysql://$DB_USER:$DB_PASS@localhost:3306/$DB_NAME
DATABASE_HOST=localhost
DATABASE_PORT=3306
DATABASE_NAME=$DB_NAME
DATABASE_USER=$DB_USER
DATABASE_PASSWORD=$DB_PASS

# 应用配置
DEBUG=false
SECRET_KEY=$(openssl rand -hex 32)

# DeepSeek API配置（请手动填写）
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_BASE_URL=https://api.deepseek.com
DEEPSEEK_MODEL=deepseek-chat

# 服务器配置
HOST=0.0.0.0
PORT=8000
WORKERS=4

# 日志配置
LOG_LEVEL=INFO
LOG_FILE_PATH=/var/log/campusguard/campusguard.log
LOG_ROTATION=1 week
LOG_RETENTION=30 days

# CORS配置（根据实际情况修改）
BACKEND_CORS_ORIGINS=["http://localhost:3000","https://your-domain.com"]
EOF

log_success "环境配置文件已创建: $ENV_FILE"

# 设置文件权限
chmod 600 "$ENV_FILE"
log_info "已设置环境配置文件权限为 600"

# 初始化Alembic（如果需要）
if [ ! -d "alembic/versions" ] || [ -z "$(ls -A alembic/versions 2>/dev/null)" ]; then
    log_info "初始化数据库迁移..."
    
    # 设置环境变量
    export $(grep -v '^#' "$ENV_FILE" | xargs)
    
    # 初始化Alembic
    python manage_db.py init-alembic
    
    # 创建初始迁移
    python manage_db.py create-migration "Initial production schema"
    
    # 应用迁移
    python manage_db.py upgrade
    
    log_success "数据库迁移初始化完成"
else
    log_info "检测到现有迁移文件，跳过初始化"
    echo -n "是否要应用现有迁移? (Y/n): "
    read -r response
    if [[ ! "$response" =~ ^[Nn]$ ]]; then
        export $(grep -v '^#' "$ENV_FILE" | xargs)
        python manage_db.py upgrade
        log_success "迁移应用完成"
    fi
fi

# 创建日志目录
log_info "创建日志目录..."
sudo mkdir -p /var/log/campusguard
sudo chown $USER:$USER /var/log/campusguard
log_success "日志目录创建完成"

# 创建备份目录
log_info "创建备份目录..."
sudo mkdir -p /var/backups/campusguard
sudo chown $USER:$USER /var/backups/campusguard
log_success "备份目录创建完成"

# 创建备份脚本
log_info "创建自动备份脚本..."
BACKUP_SCRIPT="/usr/local/bin/campusguard_backup.sh"
sudo tee "$BACKUP_SCRIPT" > /dev/null << EOF
#!/bin/bash
# CampusGuard 自动备份脚本

DB_NAME="$DB_NAME"
DB_USER="$DB_USER"
DB_PASS="$DB_PASS"
BACKUP_DIR="/var/backups/campusguard"
DATE=\$(date +%Y%m%d_%H%M%S)

# 创建备份
mysqldump -u \$DB_USER -p\$DB_PASS \$DB_NAME > \$BACKUP_DIR/campusguard_\$DATE.sql

# 压缩备份
gzip \$BACKUP_DIR/campusguard_\$DATE.sql

# 删除7天前的备份
find \$BACKUP_DIR -name "campusguard_*.sql.gz" -mtime +7 -delete

echo "备份完成: \$BACKUP_DIR/campusguard_\$DATE.sql.gz"
EOF

sudo chmod +x "$BACKUP_SCRIPT"
log_success "备份脚本创建完成: $BACKUP_SCRIPT"

# 设置定时备份
log_info "设置定时备份..."
(crontab -l 2>/dev/null; echo "0 2 * * * $BACKUP_SCRIPT") | crontab -
log_success "定时备份已设置（每天凌晨2点）"

# 安全建议
log_info "生成安全配置建议..."
cat << EOF

🔒 安全配置建议:

1. 数据库安全:
   - 定期更新数据库密码
   - 启用SSL连接
   - 限制数据库用户权限
   - 定期备份验证

2. 应用安全:
   - 更新 DEEPSEEK_API_KEY
   - 修改 SECRET_KEY（已自动生成）
   - 配置防火墙规则
   - 启用HTTPS

3. 系统安全:
   - 定期更新系统补丁
   - 配置日志监控
   - 设置入侵检测
   - 限制SSH访问

4. 监控配置:
   - 设置数据库监控
   - 配置应用性能监控
   - 设置告警通知
   - 定期检查备份

EOF

log_success "生产环境数据库初始化完成！"
log_info "配置文件位置: $ENV_FILE"
log_info "备份脚本位置: $BACKUP_SCRIPT"
log_info "请记得更新配置文件中的 DEEPSEEK_API_KEY"

# 最终验证
log_info "执行最终验证..."
export $(grep -v '^#' "$ENV_FILE" | xargs)

if python -c "
from app.core.database import engine
from sqlalchemy import text
try:
    with engine.connect() as conn:
        result = conn.execute(text('SELECT 1'))
        print('数据库连接验证成功')
except Exception as e:
    print(f'数据库连接验证失败: {e}')
    exit(1)
"; then
    log_success "所有验证通过，生产环境已就绪！"
else
    log_error "验证失败，请检查配置"
    exit 1
fi