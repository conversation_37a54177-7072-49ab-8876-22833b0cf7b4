# CampusGuard 数据库管理指南

## 概述

本指南详细介绍了CampusGuard项目的数据库管理操作，包括首次设置、迁移管理、备份恢复等完整流程。

## 目录

1. [环境准备](#环境准备)
2. [首次数据库设置](#首次数据库设置)
3. [数据库迁移管理](#数据库迁移管理)
4. [日常维护操作](#日常维护操作)
5. [备份与恢复](#备份与恢复)
6. [故障排除](#故障排除)
7. [最佳实践](#最佳实践)

## 环境准备

### 1. 数据库服务器要求

```bash
# MySQL 8.0+ 推荐配置
MySQL版本: 8.0+
字符集: utf8mb4
排序规则: utf8mb4_unicode_ci
时区: UTC
```

### 2. Python环境要求

```bash
# Python版本和依赖
Python: 3.13.2+
依赖包: 见 requirements.txt
```

### 3. 环境变量配置

创建 `backend/config/.env` 文件：

```bash
# 数据库配置
DATABASE_URL=mysql+pymysql://campusguard:campusguard123@localhost:3306/campusguard
DATABASE_HOST=localhost
DATABASE_PORT=3306
DATABASE_NAME=campusguard
DATABASE_USER=campusguard
DATABASE_PASSWORD=campusguard123

# DeepSeek API配置
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_BASE_URL=https://api.deepseek.com
DEEPSEEK_MODEL=deepseek-chat

# 应用配置
DEBUG=true
SECRET_KEY=campusguard-secret-key-2024
```

## 首次数据库设置

### 方法一：快速设置（推荐用于开发环境）

```bash
# 进入后端目录
cd backend

# 一键完成数据库设置（创建表 + 示例数据）
python manage_db.py full-setup
```

这个命令会：
- 创建所有数据库表
- 插入示例设备数据
- 插入示例告警数据
- 创建网络拓扑数据

### 方法二：分步设置（推荐用于生产环境）

#### 步骤1：创建数据库和用户

```sql
-- 连接到MySQL服务器
mysql -u root -p

-- 创建数据库
CREATE DATABASE campusguard CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户并授权
CREATE USER 'campusguard'@'localhost' IDENTIFIED BY 'campusguard123';
GRANT ALL PRIVILEGES ON campusguard.* TO 'campusguard'@'localhost';
FLUSH PRIVILEGES;

-- 退出MySQL
EXIT;
```

#### 步骤2：初始化Alembic环境

```bash
# 初始化迁移环境
python manage_db.py init-alembic

# 创建初始迁移脚本
python manage_db.py create-migration "Initial database schema"

# 应用迁移
python manage_db.py upgrade
```

#### 步骤3：插入初始数据（可选）

```bash
# 仅插入示例数据
python manage_db.py init-sample-data
```

### 验证安装

```bash
# 检查数据库连接
python -c "
from app.core.database import engine
from sqlalchemy import text
with engine.connect() as conn:
    result = conn.execute(text('SELECT COUNT(*) FROM devices'))
    print(f'设备数量: {result.scalar()}')
"

# 启动应用测试
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

## 数据库迁移管理

### 创建新迁移

当你修改了数据模型后：

```bash
# 自动生成迁移脚本
python manage_db.py create-migration "Add new column to devices table"

# 手动检查生成的迁移文件
ls alembic/versions/

# 查看迁移内容
cat alembic/versions/最新的迁移文件.py
```

### 应用迁移

```bash
# 升级到最新版本
python manage_db.py upgrade

# 升级到特定版本
python manage_db.py upgrade <revision_id>

# 查看当前版本
python manage_db.py current

# 查看迁移历史
python manage_db.py history
```

### 回滚迁移

```bash
# 回滚到上一个版本
python manage_db.py downgrade -1

# 回滚到特定版本
python manage_db.py downgrade <revision_id>

# 回滚到基础版本（清空所有表）
python manage_db.py downgrade base
```

### 迁移最佳实践

1. **迁移前备份**
```bash
# 备份数据库
mysqldump -u campusguard -p campusguard > backup_$(date +%Y%m%d_%H%M%S).sql
```

2. **测试迁移**
```bash
# 在测试环境先验证
python manage_db.py upgrade
python manage_db.py downgrade -1
python manage_db.py upgrade
```

3. **生产环境迁移流程**
```bash
# 1. 备份数据库
mysqldump -u campusguard -p campusguard > production_backup_$(date +%Y%m%d_%H%M%S).sql

# 2. 停止应用服务
sudo systemctl stop campusguard

# 3. 应用迁移
python manage_db.py upgrade

# 4. 验证迁移
python manage_db.py current

# 5. 启动应用服务
sudo systemctl start campusguard

# 6. 验证应用功能
curl http://localhost:8000/health
```

## 日常维护操作

### 数据库状态检查

```bash
# 检查表结构
python -c "
from app.core.database import engine
from sqlalchemy import inspect
inspector = inspect(engine)
for table in inspector.get_table_names():
    print(f'表: {table}')
    for column in inspector.get_columns(table):
        print(f'  - {column[\"name\"]}: {column[\"type\"]}')
"

# 检查数据统计
python -c "
from app.core.database import SessionLocal
from app.models import Device, Alert, PerformanceMetric
with SessionLocal() as db:
    print(f'设备数量: {db.query(Device).count()}')
    print(f'告警数量: {db.query(Alert).count()}')
    print(f'性能指标数量: {db.query(PerformanceMetric).count()}')
"
```

### 数据清理

```bash
# 清理旧的性能数据（保留30天）
python -c "
from datetime import datetime, timedelta
from app.core.database import SessionLocal
from app.models import PerformanceMetric
cutoff_date = datetime.now() - timedelta(days=30)
with SessionLocal() as db:
    old_metrics = db.query(PerformanceMetric).filter(
        PerformanceMetric.timestamp < cutoff_date.isoformat()
    ).delete()
    db.commit()
    print(f'清理了 {old_metrics} 条旧性能数据')
"

# 清理已解决的旧告警（保留90天）
python -c "
from datetime import datetime, timedelta
from app.core.database import SessionLocal
from app.models import Alert, AlertStatus
cutoff_date = datetime.now() - timedelta(days=90)
with SessionLocal() as db:
    old_alerts = db.query(Alert).filter(
        Alert.status == AlertStatus.RESOLVED,
        Alert.resolved_at < cutoff_date.isoformat()
    ).update({'is_active': False})
    db.commit()
    print(f'软删除了 {old_alerts} 条旧告警')
"
```

### 性能优化

```bash
# 分析表大小
mysql -u campusguard -p campusguard -e "
SELECT 
    table_name AS 'Table',
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'
FROM information_schema.TABLES 
WHERE table_schema = 'campusguard'
ORDER BY (data_length + index_length) DESC;
"

# 优化表
mysql -u campusguard -p campusguard -e "
OPTIMIZE TABLE devices, alerts, performance_metrics;
"

# 分析慢查询
mysql -u campusguard -p campusguard -e "
SHOW VARIABLES LIKE 'slow_query_log';
SHOW VARIABLES LIKE 'long_query_time';
"
```

## 备份与恢复

### 自动备份脚本

创建 `backend/scripts/backup_db.sh`：

```bash
#!/bin/bash

# 配置
DB_NAME="campusguard"
DB_USER="campusguard"
DB_PASS="campusguard123"
BACKUP_DIR="/var/backups/campusguard"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 执行备份
mysqldump -u $DB_USER -p$DB_PASS $DB_NAME > $BACKUP_DIR/campusguard_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/campusguard_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "campusguard_*.sql.gz" -mtime +7 -delete

echo "备份完成: $BACKUP_DIR/campusguard_$DATE.sql.gz"
```

### 设置定时备份

```bash
# 添加到crontab
crontab -e

# 每天凌晨2点备份
0 2 * * * /path/to/backend/scripts/backup_db.sh
```

### 恢复数据库

```bash
# 从备份恢复
gunzip -c /var/backups/campusguard/campusguard_20241201_020000.sql.gz | mysql -u campusguard -p campusguard

# 或者从未压缩的备份恢复
mysql -u campusguard -p campusguard < backup_file.sql
```

## 故障排除

### 常见问题

#### 1. 连接失败

```bash
# 检查MySQL服务状态
sudo systemctl status mysql

# 检查端口监听
netstat -tlnp | grep 3306

# 测试连接
mysql -u campusguard -p -h localhost campusguard
```

#### 2. 迁移失败

```bash
# 查看详细错误
python manage_db.py upgrade --verbose

# 检查迁移状态
python manage_db.py current

# 手动修复迁移表
mysql -u campusguard -p campusguard -e "SELECT * FROM alembic_version;"
```

#### 3. 性能问题

```bash
# 检查慢查询
mysql -u campusguard -p campusguard -e "SHOW PROCESSLIST;"

# 分析查询计划
mysql -u campusguard -p campusguard -e "EXPLAIN SELECT * FROM devices WHERE status = 'ONLINE';"

# 检查索引使用
mysql -u campusguard -p campusguard -e "SHOW INDEX FROM devices;"
```

### 日志分析

```bash
# 查看应用日志
tail -f logs/campusguard.log

# 查看MySQL错误日志
sudo tail -f /var/log/mysql/error.log

# 查看慢查询日志
sudo tail -f /var/log/mysql/mysql-slow.log
```

## 最佳实践

### 开发环境

1. **使用快速设置**
```bash
python manage_db.py full-setup
```

2. **频繁重置数据库**
```bash
# 重置开发数据库
python manage_db.py downgrade base
python manage_db.py upgrade
python manage_db.py init-sample-data
```

### 测试环境

1. **使用独立数据库**
```bash
# 创建测试数据库
CREATE DATABASE campusguard_test;
```

2. **自动化测试脚本**
```bash
#!/bin/bash
export DATABASE_URL="mysql+pymysql://campusguard:campusguard123@localhost:3306/campusguard_test"
python manage_db.py full-setup
python -m pytest tests/
```

### 生产环境

1. **严格的迁移流程**
   - 总是先备份
   - 在测试环境验证
   - 维护窗口执行
   - 回滚计划准备

2. **监控和告警**
```bash
# 设置数据库监控
# - 连接数监控
# - 慢查询监控
# - 磁盘空间监控
# - 备份状态监控
```

3. **定期维护**
```bash
# 每周执行的维护任务
# - 数据清理
# - 表优化
# - 索引分析
# - 备份验证
```

## 安全建议

1. **数据库用户权限**
```sql
-- 生产环境使用最小权限原则
GRANT SELECT, INSERT, UPDATE, DELETE ON campusguard.* TO 'campusguard_app'@'localhost';
```

2. **连接安全**
```bash
# 使用SSL连接
DATABASE_URL=mysql+pymysql://user:pass@host:3306/db?ssl_ca=/path/to/ca.pem
```

3. **备份加密**
```bash
# 加密备份文件
mysqldump -u campusguard -p campusguard | gpg --cipher-algo AES256 --compress-algo 1 --symmetric > backup.sql.gpg
```

## 自动化脚本

项目提供了完整的自动化脚本来简化数据库管理：

### 生产环境初始化脚本
```bash
# 一键初始化生产环境数据库
./scripts/init_production_db.sh campusguard campusguard your_password
```

### 健康检查脚本
```bash
# 全面的数据库健康检查
python scripts/db_health_check.py --verbose

# 生成JSON报告
python scripts/db_health_check.py --format json --output health_report.json
```

### 维护脚本
```bash
# 预览维护操作
python scripts/db_maintenance.py --dry-run

# 执行完整维护
python scripts/db_maintenance.py --verbose --output maintenance_report.txt
```

详细使用说明请参考 `scripts/README.md`

## 监控和告警

### 设置定时健康检查
```bash
# 每小时检查一次
echo "0 * * * * cd /path/to/backend && python scripts/db_health_check.py || echo 'DB Health Check Failed' | mail -s 'CampusGuard Alert' <EMAIL>" | crontab -
```

### 设置定期维护
```bash
# 每周日凌晨3点执行维护
echo "0 3 * * 0 cd /path/to/backend && python scripts/db_maintenance.py" | crontab -
```

## 联系支持

如果遇到无法解决的问题，请：

1. 运行健康检查脚本收集诊断信息
2. 收集相关错误日志
3. 记录重现步骤
4. 提供环境信息
5. 联系开发团队

---

**最后更新**: 2025-01-01  
**版本**: 1.0.0  
**维护者**: CampusGuard开发团队