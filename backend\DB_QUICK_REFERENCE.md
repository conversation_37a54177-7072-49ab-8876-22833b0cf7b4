# 数据库管理快速参考

## 🚀 快速开始

```bash
# 首次设置（开发环境）
cd backend
python manage_db.py full-setup

# 启动应用
uvicorn app.main:app --reload
```

## 📋 常用命令

### 数据库设置
```bash
# 完整设置（表+数据）
python manage_db.py full-setup

# 仅创建表
python manage_db.py create-tables

# 仅添加示例数据
python manage_db.py init-sample-data
```

### 迁移管理
```bash
# 创建迁移
python manage_db.py create-migration "描述信息"

# 应用迁移
python manage_db.py upgrade

# 查看状态
python manage_db.py current

# 查看历史
python manage_db.py history

# 回滚
python manage_db.py downgrade -1
```

### Alembic操作
```bash
# 初始化Alembic
python manage_db.py init-alembic

# 升级到指定版本
python manage_db.py upgrade <revision_id>

# 回滚到指定版本
python manage_db.py downgrade <revision_id>
```

## 🔧 故障排除

### 连接问题
```bash
# 测试数据库连接
mysql -u campusguard -p campusguard

# 检查配置
cat config/.env | grep DATABASE
```

### 迁移问题
```bash
# 查看迁移状态
python manage_db.py current

# 重置迁移（危险操作）
python manage_db.py downgrade base
python manage_db.py upgrade
```

### 数据问题
```bash
# 检查表数据
python -c "
from app.core.database import SessionLocal
from app.models import Device, Alert
with SessionLocal() as db:
    print(f'设备: {db.query(Device).count()}')
    print(f'告警: {db.query(Alert).count()}')
"
```

## 💾 备份恢复

### 备份
```bash
# 手动备份
mysqldump -u campusguard -p campusguard > backup_$(date +%Y%m%d).sql

# 压缩备份
mysqldump -u campusguard -p campusguard | gzip > backup_$(date +%Y%m%d).sql.gz
```

### 恢复
```bash
# 从备份恢复
mysql -u campusguard -p campusguard < backup_file.sql

# 从压缩备份恢复
gunzip -c backup_file.sql.gz | mysql -u campusguard -p campusguard
```

## 🔍 监控检查

### 数据库状态
```bash
# 检查表大小
mysql -u campusguard -p campusguard -e "
SELECT table_name, 
       ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'
FROM information_schema.TABLES 
WHERE table_schema = 'campusguard';"

# 检查连接数
mysql -u campusguard -p campusguard -e "SHOW PROCESSLIST;"
```

### 应用状态
```bash
# 健康检查
curl http://localhost:8000/health

# API文档
curl http://localhost:8000/api/v1/docs
```

## ⚠️ 生产环境注意事项

### 迁移前检查清单
- [ ] 数据库已备份
- [ ] 测试环境验证通过
- [ ] 应用服务已停止
- [ ] 回滚计划准备就绪

### 迁移执行
```bash
# 1. 备份
mysqldump -u campusguard -p campusguard > prod_backup_$(date +%Y%m%d_%H%M%S).sql

# 2. 停止服务
sudo systemctl stop campusguard

# 3. 执行迁移
python manage_db.py upgrade

# 4. 验证
python manage_db.py current

# 5. 启动服务
sudo systemctl start campusguard

# 6. 验证应用
curl http://localhost:8000/health
```

## 📞 紧急联系

如果遇到严重问题：
1. 立即停止应用服务
2. 保留错误日志
3. 不要执行危险操作
4. 联系开发团队