import request from './request'
import type { Alert, AlertStats, AlertType, AlertSeverity, AlertStatus } from '@/types/alert'

export interface AlertListParams {
  skip?: number
  limit?: number
  severity?: AlertSeverity
  status?: AlertStatus
  alert_type?: AlertType
  device_id?: number
}

export interface AlertCreateData {
  title: string
  description: string
  alert_type: AlertType
  severity: AlertSeverity
  device_id?: number
  device_ip?: string
}

export const alertApi = {
  // Get alert list
  getAlerts(params?: AlertListParams) {
    return request.get<Alert[]>('/alerts', { params })
  },

  // Create alert
  createAlert(data: AlertCreateData) {
    return request.post<Alert>('/alerts', data)
  },

  // Acknowledge alert
  acknowledgeAlert(id: number) {
    return request.put(`/alerts/${id}/acknowledge`)
  },

  // Resolve alert
  resolveAlert(id: number, resolutionNotes?: string) {
    return request.put(`/alerts/${id}/resolve`, { resolution_notes: resolutionNotes })
  },

  // Get alert statistics
  getStats() {
    return request.get<AlertStats>('/alerts/stats')
  },

  // Get alert statistics with time range
  getStatistics(hours = 24) {
    return request.get('/alerts/statistics', { params: { hours } })
  },

  // Escalate alerts
  escalateAlerts() {
    return request.post('/alerts/escalate')
  },

  // Cleanup old alerts
  cleanupOldAlerts(days = 30) {
    return request.delete('/alerts/cleanup', { params: { days } })
  }
}
