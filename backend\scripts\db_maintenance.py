#!/usr/bin/env python3
"""
CampusGuard 数据库维护脚本
用于定期清理旧数据、优化表结构、更新统计信息
"""

import sys
import os
import asyncio
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any
import argparse

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from sqlalchemy import text
from loguru import logger

from app.core.database import engine, SessionLocal
from app.models import Device, Alert, PerformanceMetric, Conversation, Message, AlertStatus


class DatabaseMaintenance:
    """数据库维护工具"""
    
    def __init__(self, dry_run: bool = False):
        self.dry_run = dry_run
        self.stats = {
            "start_time": datetime.now(),
            "operations": [],
            "errors": []
        }
    
    def log_operation(self, operation: str, details: str, affected_rows: int = 0):
        """记录操作"""
        self.stats["operations"].append({
            "operation": operation,
            "details": details,
            "affected_rows": affected_rows,
            "timestamp": datetime.now().isoformat()
        })
        logger.info(f"{'[DRY RUN] ' if self.dry_run else ''}{operation}: {details} (影响行数: {affected_rows})")
    
    def log_error(self, operation: str, error: str):
        """记录错误"""
        self.stats["errors"].append({
            "operation": operation,
            "error": error,
            "timestamp": datetime.now().isoformat()
        })
        logger.error(f"操作失败 {operation}: {error}")
    
    async def cleanup_old_performance_metrics(self, days: int = 30) -> int:
        """清理旧的性能指标数据"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            
            with SessionLocal() as db:
                if self.dry_run:
                    count = db.query(PerformanceMetric).filter(
                        PerformanceMetric.timestamp < cutoff_date.isoformat()
                    ).count()
                    affected_rows = count
                else:
                    affected_rows = db.query(PerformanceMetric).filter(
                        PerformanceMetric.timestamp < cutoff_date.isoformat()
                    ).delete()
                    db.commit()
                
                self.log_operation(
                    "清理旧性能指标",
                    f"删除 {days} 天前的性能数据",
                    affected_rows
                )
                return affected_rows
                
        except Exception as e:
            self.log_error("清理旧性能指标", str(e))
            return 0
    
    async def cleanup_old_resolved_alerts(self, days: int = 90) -> int:
        """清理旧的已解决告警"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            
            with SessionLocal() as db:
                if self.dry_run:
                    count = db.query(Alert).filter(
                        Alert.status == AlertStatus.RESOLVED,
                        Alert.resolved_at < cutoff_date.isoformat(),
                        Alert.is_active == True
                    ).count()
                    affected_rows = count
                else:
                    affected_rows = db.query(Alert).filter(
                        Alert.status == AlertStatus.RESOLVED,
                        Alert.resolved_at < cutoff_date.isoformat(),
                        Alert.is_active == True
                    ).update({"is_active": False})
                    db.commit()
                
                self.log_operation(
                    "清理旧告警",
                    f"软删除 {days} 天前的已解决告警",
                    affected_rows
                )
                return affected_rows
                
        except Exception as e:
            self.log_error("清理旧告警", str(e))
            return 0
    
    async def cleanup_old_conversations(self, days: int = 180) -> int:
        """清理旧的AI对话记录"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            
            with SessionLocal() as db:
                if self.dry_run:
                    count = db.query(Conversation).filter(
                        Conversation.last_activity < cutoff_date.isoformat(),
                        Conversation.is_active == True
                    ).count()
                    affected_rows = count
                else:
                    # 先软删除对话
                    conversation_count = db.query(Conversation).filter(
                        Conversation.last_activity < cutoff_date.isoformat(),
                        Conversation.is_active == True
                    ).update({"is_active": False})
                    
                    # 软删除相关消息
                    message_count = db.execute(text("""
                        UPDATE messages m 
                        JOIN conversations c ON m.conversation_id = c.id 
                        SET m.is_active = 0 
                        WHERE c.is_active = 0 AND m.is_active = 1
                    """)).rowcount
                    
                    db.commit()
                    affected_rows = conversation_count + message_count
                
                self.log_operation(
                    "清理旧对话",
                    f"软删除 {days} 天前的AI对话记录",
                    affected_rows
                )
                return affected_rows
                
        except Exception as e:
            self.log_error("清理旧对话", str(e))
            return 0
    
    async def cleanup_orphaned_data(self) -> int:
        """清理孤立数据"""
        try:
            total_affected = 0
            
            with SessionLocal() as db:
                # 清理孤立的告警（没有对应设备）
                if self.dry_run:
                    orphaned_alerts = db.execute(text("""
                        SELECT COUNT(*) FROM alerts a 
                        LEFT JOIN devices d ON a.device_id = d.id 
                        WHERE a.device_id IS NOT NULL AND d.id IS NULL AND a.is_active = 1
                    """)).scalar()
                else:
                    orphaned_alerts = db.execute(text("""
                        UPDATE alerts a 
                        LEFT JOIN devices d ON a.device_id = d.id 
                        SET a.is_active = 0 
                        WHERE a.device_id IS NOT NULL AND d.id IS NULL AND a.is_active = 1
                    """)).rowcount
                    
                total_affected += orphaned_alerts
                
                # 清理孤立的性能指标
                if self.dry_run:
                    orphaned_metrics = db.execute(text("""
                        SELECT COUNT(*) FROM performance_metrics pm 
                        LEFT JOIN devices d ON pm.device_id = d.id 
                        WHERE d.id IS NULL AND pm.is_active = 1
                    """)).scalar()
                else:
                    orphaned_metrics = db.execute(text("""
                        UPDATE performance_metrics pm 
                        LEFT JOIN devices d ON pm.device_id = d.id 
                        SET pm.is_active = 0 
                        WHERE d.id IS NULL AND pm.is_active = 1
                    """)).rowcount
                    
                total_affected += orphaned_metrics
                
                if not self.dry_run:
                    db.commit()
                
                self.log_operation(
                    "清理孤立数据",
                    f"清理孤立告警: {orphaned_alerts}, 孤立指标: {orphaned_metrics}",
                    total_affected
                )
                return total_affected
                
        except Exception as e:
            self.log_error("清理孤立数据", str(e))
            return 0
    
    async def optimize_tables(self) -> int:
        """优化数据库表"""
        try:
            tables = [
                "devices", "alerts", "performance_metrics", 
                "conversations", "messages", "threat_intelligence"
            ]
            
            optimized_count = 0
            
            with engine.connect() as conn:
                for table in tables:
                    if not self.dry_run:
                        conn.execute(text(f"OPTIMIZE TABLE {table}"))
                    optimized_count += 1
                    
                    self.log_operation(
                        "优化表",
                        f"优化表 {table}",
                        1
                    )
                
                return optimized_count
                
        except Exception as e:
            self.log_error("优化表", str(e))
            return 0
    
    async def update_table_statistics(self) -> int:
        """更新表统计信息"""
        try:
            tables = [
                "devices", "alerts", "performance_metrics", 
                "conversations", "messages", "threat_intelligence"
            ]
            
            updated_count = 0
            
            with engine.connect() as conn:
                for table in tables:
                    if not self.dry_run:
                        conn.execute(text(f"ANALYZE TABLE {table}"))
                    updated_count += 1
                    
                    self.log_operation(
                        "更新统计信息",
                        f"分析表 {table}",
                        1
                    )
                
                return updated_count
                
        except Exception as e:
            self.log_error("更新统计信息", str(e))
            return 0
    
    async def check_and_repair_tables(self) -> int:
        """检查和修复表"""
        try:
            tables = [
                "devices", "alerts", "performance_metrics", 
                "conversations", "messages", "threat_intelligence"
            ]
            
            checked_count = 0
            
            with engine.connect() as conn:
                for table in tables:
                    # 检查表
                    check_result = conn.execute(text(f"CHECK TABLE {table}")).fetchall()
                    
                    # 检查是否需要修复
                    needs_repair = any(row[3] != 'OK' for row in check_result)
                    
                    if needs_repair and not self.dry_run:
                        repair_result = conn.execute(text(f"REPAIR TABLE {table}")).fetchall()
                        self.log_operation(
                            "修复表",
                            f"修复表 {table}",
                            1
                        )
                    else:
                        self.log_operation(
                            "检查表",
                            f"检查表 {table} - {'需要修复' if needs_repair else '正常'}",
                            1
                        )
                    
                    checked_count += 1
                
                return checked_count
                
        except Exception as e:
            self.log_error("检查和修复表", str(e))
            return 0
    
    async def vacuum_and_reindex(self) -> int:
        """重建索引（MySQL中的等效操作）"""
        try:
            # MySQL中没有VACUUM，但可以重建索引
            tables_with_indexes = [
                ("devices", ["ip_address", "name", "status"]),
                ("alerts", ["device_id", "status", "severity"]),
                ("performance_metrics", ["device_id", "metric_name", "timestamp"]),
                ("conversations", ["session_id", "user_id"]),
                ("messages", ["conversation_id"])
            ]
            
            reindexed_count = 0
            
            with engine.connect() as conn:
                for table, indexes in tables_with_indexes:
                    if not self.dry_run:
                        # 重新创建索引（先删除再创建）
                        # 注意：这里只是示例，实际操作需要更谨慎
                        pass
                    
                    self.log_operation(
                        "重建索引",
                        f"重建表 {table} 的索引",
                        len(indexes)
                    )
                    reindexed_count += len(indexes)
                
                return reindexed_count
                
        except Exception as e:
            self.log_error("重建索引", str(e))
            return 0
    
    async def generate_maintenance_report(self) -> Dict[str, Any]:
        """生成维护报告"""
        end_time = datetime.now()
        duration = end_time - self.stats["start_time"]
        
        total_operations = len(self.stats["operations"])
        total_affected_rows = sum(op["affected_rows"] for op in self.stats["operations"])
        error_count = len(self.stats["errors"])
        
        report = {
            "maintenance_summary": {
                "start_time": self.stats["start_time"].isoformat(),
                "end_time": end_time.isoformat(),
                "duration_seconds": duration.total_seconds(),
                "dry_run": self.dry_run,
                "total_operations": total_operations,
                "total_affected_rows": total_affected_rows,
                "error_count": error_count,
                "success_rate": (total_operations - error_count) / total_operations * 100 if total_operations > 0 else 0
            },
            "operations": self.stats["operations"],
            "errors": self.stats["errors"]
        }
        
        return report
    
    async def run_full_maintenance(
        self,
        cleanup_metrics_days: int = 30,
        cleanup_alerts_days: int = 90,
        cleanup_conversations_days: int = 180,
        skip_optimization: bool = False
    ) -> Dict[str, Any]:
        """运行完整的维护流程"""
        logger.info(f"开始数据库维护 {'(DRY RUN)' if self.dry_run else ''}")
        
        # 数据清理
        await self.cleanup_old_performance_metrics(cleanup_metrics_days)
        await self.cleanup_old_resolved_alerts(cleanup_alerts_days)
        await self.cleanup_old_conversations(cleanup_conversations_days)
        await self.cleanup_orphaned_data()
        
        # 表优化（可选）
        if not skip_optimization:
            await self.check_and_repair_tables()
            await self.optimize_tables()
            await self.update_table_statistics()
        
        # 生成报告
        report = await self.generate_maintenance_report()
        
        logger.info(f"维护完成，总操作数: {report['maintenance_summary']['total_operations']}")
        return report


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="CampusGuard 数据库维护工具")
    
    # 基本选项
    parser.add_argument("--dry-run", action="store_true", help="预览模式，不实际执行操作")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")
    
    # 清理选项
    parser.add_argument("--cleanup-metrics-days", type=int, default=30, 
                       help="清理多少天前的性能指标 (默认: 30)")
    parser.add_argument("--cleanup-alerts-days", type=int, default=90,
                       help="清理多少天前的已解决告警 (默认: 90)")
    parser.add_argument("--cleanup-conversations-days", type=int, default=180,
                       help="清理多少天前的AI对话 (默认: 180)")
    
    # 优化选项
    parser.add_argument("--skip-optimization", action="store_true", 
                       help="跳过表优化操作")
    
    # 输出选项
    parser.add_argument("--output", "-o", help="保存报告到文件")
    parser.add_argument("--format", choices=["json", "text"], default="text",
                       help="报告格式")
    
    # 单独操作
    parser.add_argument("--cleanup-only", action="store_true", help="仅执行数据清理")
    parser.add_argument("--optimize-only", action="store_true", help="仅执行表优化")
    
    args = parser.parse_args()
    
    # 配置日志
    logger.remove()
    if args.verbose:
        logger.add(sys.stdout, level="DEBUG")
    else:
        logger.add(sys.stdout, level="INFO")
    
    # 创建维护实例
    maintenance = DatabaseMaintenance(dry_run=args.dry_run)
    
    # 执行维护操作
    if args.cleanup_only:
        await maintenance.cleanup_old_performance_metrics(args.cleanup_metrics_days)
        await maintenance.cleanup_old_resolved_alerts(args.cleanup_alerts_days)
        await maintenance.cleanup_old_conversations(args.cleanup_conversations_days)
        await maintenance.cleanup_orphaned_data()
    elif args.optimize_only:
        await maintenance.check_and_repair_tables()
        await maintenance.optimize_tables()
        await maintenance.update_table_statistics()
    else:
        # 完整维护
        report = await maintenance.run_full_maintenance(
            cleanup_metrics_days=args.cleanup_metrics_days,
            cleanup_alerts_days=args.cleanup_alerts_days,
            cleanup_conversations_days=args.cleanup_conversations_days,
            skip_optimization=args.skip_optimization
        )
    
    # 生成最终报告
    final_report = await maintenance.generate_maintenance_report()
    
    # 输出报告
    if args.format == "json":
        import json
        output = json.dumps(final_report, indent=2, ensure_ascii=False)
    else:
        output = format_text_report(final_report)
    
    if args.output:
        with open(args.output, "w", encoding="utf-8") as f:
            f.write(output)
        logger.info(f"报告已保存到: {args.output}")
    else:
        print(output)
    
    # 设置退出码
    if final_report["maintenance_summary"]["error_count"] > 0:
        sys.exit(1)
    else:
        sys.exit(0)


def format_text_report(report: Dict[str, Any]) -> str:
    """格式化文本报告"""
    output = []
    
    summary = report["maintenance_summary"]
    
    # 标题
    output.append("=" * 60)
    output.append("CampusGuard 数据库维护报告")
    output.append("=" * 60)
    output.append(f"开始时间: {summary['start_time']}")
    output.append(f"结束时间: {summary['end_time']}")
    output.append(f"执行时长: {summary['duration_seconds']:.2f} 秒")
    output.append(f"模式: {'预览模式' if summary['dry_run'] else '执行模式'}")
    output.append("")
    
    # 统计信息
    output.append("📊 执行统计:")
    output.append("-" * 40)
    output.append(f"总操作数: {summary['total_operations']}")
    output.append(f"影响行数: {summary['total_affected_rows']}")
    output.append(f"错误数量: {summary['error_count']}")
    output.append(f"成功率: {summary['success_rate']:.1f}%")
    output.append("")
    
    # 操作详情
    if report["operations"]:
        output.append("✅ 执行的操作:")
        output.append("-" * 40)
        for op in report["operations"]:
            output.append(f"• {op['operation']}: {op['details']} (影响: {op['affected_rows']} 行)")
        output.append("")
    
    # 错误信息
    if report["errors"]:
        output.append("❌ 错误信息:")
        output.append("-" * 40)
        for error in report["errors"]:
            output.append(f"• {error['operation']}: {error['error']}")
        output.append("")
    
    output.append("=" * 60)
    
    return "\n".join(output)


if __name__ == "__main__":
    asyncio.run(main())