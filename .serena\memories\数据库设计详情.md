# CampusGuard数据库设计详情

## 数据库配置
- **数据库系统**：MySQL 8.0
- **ORM框架**：SQLAlchemy 2.0+
- **连接池**：PyMySQL
- **字符集**：utf8mb4

## 核心数据表设计

### 1. devices（设备信息表）
```sql
- id: Integer, 主键
- name: String(100), 设备名称
- ip_address: String(45), IP地址（唯一）
- device_type: String(50), 设备类型（switch/router/ap/server）
- location: String(100), 物理位置
- status: String(20), 状态（active/inactive/maintenance）
- snmp_community: String(100), SNMP团体字符串
- snmp_version: String(10), SNMP版本
- last_seen: DateTime, 最后在线时间
- created_at: DateTime, 创建时间
- updated_at: DateTime, 更新时间
```

### 2. alerts（告警记录表）
```sql
- id: Integer, 主键
- device_id: Integer, 外键->devices.id
- severity: String(20), 严重级别（critical/high/medium/low）
- alert_type: String(50), 告警类型
- title: String(200), 告警标题
- description: Text, 详细描述
- deepseek_analysis: Text, AI分析结果
- status: String(20), 状态（open/acknowledged/resolved）
- acknowledged_by: String(100), 确认人
- resolved_by: String(100), 解决人
- created_at: DateTime, 创建时间
- updated_at: DateTime, 更新时间
```

### 3. performance_metrics（性能指标表）
```sql
- id: Integer, 主键
- device_id: Integer, 外键->devices.id
- metric_type: String(50), 指标类型（cpu/memory/traffic）
- metric_name: String(100), 指标名称
- metric_value: Float, 指标值
- unit: String(20), 单位
- timestamp: DateTime, 采集时间
```

### 4. threat_intelligence（威胁情报表）
```sql
- id: Integer, 主键
- ip_address: String(45), IP地址
- threat_level: String(20), 威胁级别
- threat_type: String(100), 威胁类型
- source: String(100), 情报来源
- description: Text, 威胁描述
- first_seen: DateTime, 首次发现
- last_seen: DateTime, 最后更新
- is_active: Boolean, 是否活跃
```

### 5. ai_conversations（AI对话表）
```sql
- id: Integer, 主键
- conversation_id: String(100), 会话ID（唯一）
- agent_type: String(50), Agent类型
- status: String(20), 状态
- created_at: DateTime, 创建时间
- updated_at: DateTime, 更新时间
```

### 6. ai_messages（AI消息表）
```sql
- id: Integer, 主键
- conversation_id: Integer, 外键->ai_conversations.id
- role: String(20), 角色（user/assistant/system）
- content: Text, 消息内容
- metadata: JSON, 元数据
- created_at: DateTime, 创建时间
```

### 7. network_topology（网络拓扑表）
```sql
- id: Integer, 主键
- data: JSON, 拓扑数据
- version: Integer, 版本号
- created_at: DateTime, 创建时间
- created_by: String(100), 创建人
```

### 8. ip_blacklist（IP黑名单表）
```sql
- id: Integer, 主键
- ip_address: String(45), IP地址（唯一）
- reason: String(200), 加入原因
- added_by: String(100), 添加人
- is_active: Boolean, 是否生效
- created_at: DateTime, 创建时间
- expires_at: DateTime, 过期时间
```

## 索引设计
1. devices表：
   - 唯一索引：ip_address
   - 普通索引：device_type, status

2. alerts表：
   - 复合索引：(device_id, status)
   - 普通索引：severity, created_at

3. performance_metrics表：
   - 复合索引：(device_id, metric_type, timestamp)

4. threat_intelligence表：
   - 唯一索引：ip_address
   - 普通索引：threat_level, is_active

## 数据库初始化
- 使用SQLAlchemy的create_all()自动创建表
- 提供sample data初始化脚本
- 支持数据库迁移（可选Alembic）

## 性能优化
- 合理使用索引提升查询性能
- 性能数据定期归档
- 告警历史数据分区存储（可选）
- 连接池配置优化