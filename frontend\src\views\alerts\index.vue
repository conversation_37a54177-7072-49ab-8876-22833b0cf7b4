<template>
  <div class="alert-list">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>告警管理</h1>
      <div class="header-actions">
        <a-button @click="refreshAlerts" :loading="loading" :icon="h(ReloadOutlined)">
          刷新
        </a-button>
        <a-button @click="exportAlerts" :icon="h(ExportOutlined)">
          导出
        </a-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <a-row :gutter="16" class="stats-row">
      <a-col :xs="24" :sm="12" :md="6">
        <a-card :bordered="false" :loading="statsLoading">
          <a-statistic
            title="总告警数"
            :value="alertStats.total"
            :value-style="{ color: '#1890ff' }"
          >
            <template #prefix>
              <BellOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :xs="24" :sm="12" :md="6">
        <a-card :bordered="false" :loading="statsLoading">
          <a-statistic
            title="严重告警"
            :value="alertStats.critical"
            :value-style="{ color: '#ff4d4f' }"
          >
            <template #prefix>
              <ExclamationCircleOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :xs="24" :sm="12" :md="6">
        <a-card :bordered="false" :loading="statsLoading">
          <a-statistic
            title="未确认"
            :value="alertStats.unacknowledged"
            :value-style="{ color: '#faad14' }"
          >
            <template #prefix>
              <QuestionCircleOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :xs="24" :sm="12" :md="6">
        <a-card :bordered="false" :loading="statsLoading">
          <a-statistic
            title="已解决"
            :value="alertStats.resolved"
            :value-style="{ color: '#52c41a' }"
          >
            <template #prefix>
              <CheckCircleOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
    </a-row>

    <!-- 搜索和筛选 -->
    <a-card class="filter-card" :bordered="false">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-input
            v-model:value="searchText"
            placeholder="搜索告警标题或描述"
            allow-clear
            @change="handleSearch"
            :prefix="h(SearchOutlined)"
          />
        </a-col>
        <a-col :span="6">
          <a-select
            v-model:value="filterSeverity"
            placeholder="告警级别"
            allow-clear
            style="width: 100%"
            @change="handleFilter"
          >
            <a-select-option value="">全部级别</a-select-option>
            <a-select-option value="critical">严重</a-select-option>
            <a-select-option value="high">高</a-select-option>
            <a-select-option value="medium">中</a-select-option>
            <a-select-option value="low">低</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="6">
          <a-select
            v-model:value="filterStatus"
            placeholder="告警状态"
            allow-clear
            style="width: 100%"
            @change="handleFilter"
          >
            <a-select-option value="">全部状态</a-select-option>
            <a-select-option value="active">活跃</a-select-option>
            <a-select-option value="acknowledged">已确认</a-select-option>
            <a-select-option value="resolved">已解决</a-select-option>
            <a-select-option value="suppressed">已抑制</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="6">
          <a-select
            v-model:value="filterType"
            placeholder="告警类型"
            allow-clear
            style="width: 100%"
            @change="handleFilter"
          >
            <a-select-option value="">全部类型</a-select-option>
            <a-select-option value="device_down">设备离线</a-select-option>
            <a-select-option value="high_cpu">CPU使用率高</a-select-option>
            <a-select-option value="high_memory">内存使用率高</a-select-option>
            <a-select-option value="high_disk">磁盘使用率高</a-select-option>
            <a-select-option value="network_anomaly">网络异常</a-select-option>
            <a-select-option value="security_threat">安全威胁</a-select-option>
            <a-select-option value="performance_degradation">性能下降</a-select-option>
            <a-select-option value="configuration_change">配置变更</a-select-option>
            <a-select-option value="other">其他</a-select-option>
          </a-select>
        </a-col>
      </a-row>

      <!-- 时间范围筛选 -->
      <a-row :gutter="16" style="margin-top: 16px">
        <a-col :span="12">
          <a-range-picker
            v-model:value="dateRange"
            style="width: 100%"
            :placeholder="['开始时间', '结束时间']"
            @change="handleFilter"
          />
        </a-col>
        <a-col :span="6">
          <a-select
            v-model:value="filterDevice"
            placeholder="选择设备"
            allow-clear
            show-search
            style="width: 100%"
            @change="handleFilter"
            :filter-option="filterDeviceOption"
          >
            <a-select-option v-for="device in devices" :key="device.id" :value="device.id">
              {{ device.name }} ({{ device.ip_address }})
            </a-select-option>
          </a-select>
        </a-col>
        <a-col :span="6">
          <a-space>
            <a-button type="primary" @click="handleFilter">应用筛选</a-button>
            <a-button @click="resetFilter">重置</a-button>
          </a-space>
        </a-col>
      </a-row>
    </a-card>

    <!-- 告警列表表格 -->
    <a-card :bordered="false" :loading="loading">
      <!-- 批量操作 -->
      <div class="batch-actions" v-if="selectedRowKeys.length > 0">
        <a-alert
          :message="`已选择 ${selectedRowKeys.length} 条告警`"
          type="info"
          show-icon
          closable
          @close="clearSelection"
        />
        <a-space style="margin-top: 8px">
          <a-button @click="batchAcknowledge">批量确认</a-button>
          <a-button @click="batchResolve">批量解决</a-button>
          <a-button @click="batchSuppress">批量抑制</a-button>
        </a-space>
      </div>

      <a-table
        :columns="columns"
        :data-source="filteredAlerts"
        :pagination="pagination"
        :row-key="record => record.id"
        :row-selection="{
          selectedRowKeys,
          onChange: onSelectChange
        }"
        @change="handleTableChange"
      >
        <!-- 告警标题 -->
        <template #title="{ record }">
          <a @click="viewAlert(record)" style="font-weight: 500">
            {{ record.title }}
          </a>
        </template>

        <!-- 级别 -->
        <template #severity="{ record }">
          <a-tag :color="getSeverityColor(record.severity)">
            {{ getSeverityText(record.severity) }}
          </a-tag>
        </template>

        <!-- 状态 -->
        <template #status="{ record }">
          <a-badge :status="getStatusBadge(record.status)" :text="getStatusText(record.status)" />
        </template>

        <!-- 设备 -->
        <template #device="{ record }">
          <router-link :to="`/devices/${record.device_id}`" v-if="record.device_id">
            {{ record.device_name || record.device_ip || '未知设备' }}
          </router-link>
          <span v-else>-</span>
        </template>

        <!-- 告警时间 -->
        <template #created_at="{ record }">
          <a-tooltip :title="formatDate(record.created_at)">
            {{ formatRelativeTime(record.created_at) }}
          </a-tooltip>
        </template>

        <!-- 持续时间 -->
        <template #duration="{ record }">
          {{ getDuration(record) }}
        </template>

        <!-- 操作 -->
        <template #action="{ record }">
          <a-dropdown>
            <a-button type="link" size="small">
              操作 <DownOutlined />
            </a-button>
            <template #overlay>
              <a-menu @click="({ key }) => handleMenuClick(key, record)">
                <a-menu-item key="view">
                  <EyeOutlined /> 查看详情
                </a-menu-item>
                <a-menu-item key="acknowledge" v-if="record.status === 'active'">
                  <CheckOutlined /> 确认告警
                </a-menu-item>
                <a-menu-item key="resolve" v-if="record.status !== 'resolved'">
                  <CheckCircleOutlined /> 解决告警
                </a-menu-item>
                <a-menu-item key="suppress" v-if="record.status !== 'suppressed'">
                  <StopOutlined /> 抑制告警
                </a-menu-item>
                <a-menu-divider />
                <a-menu-item key="history">
                  <HistoryOutlined /> 查看历史
                </a-menu-item>
                <a-menu-item key="related">
                  <LinkOutlined /> 相关告警
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </template>
      </a-table>
    </a-card>

    <!-- 告警详情抽屉 -->
    <a-drawer
      v-model:open="detailVisible"
      :title="`告警详情 - ${currentAlert?.title || ''}`"
      :width="720"
      :footer-style="{ textAlign: 'right' }"
    >
      <template v-if="currentAlert">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="告警ID">
            {{ currentAlert.id }}
          </a-descriptions-item>
          <a-descriptions-item label="告警级别">
            <a-tag :color="getSeverityColor(currentAlert.severity)">
              {{ getSeverityText(currentAlert.severity) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="告警状态">
            <a-badge :status="getStatusBadge(currentAlert.status)" :text="getStatusText(currentAlert.status)" />
          </a-descriptions-item>
          <a-descriptions-item label="告警类型">
            {{ getAlertTypeText(currentAlert.alert_type) }}
          </a-descriptions-item>
          <a-descriptions-item label="关联设备" :span="2">
            <router-link :to="`/devices/${currentAlert.device_id}`" v-if="currentAlert.device_id">
              {{ currentAlert.device_name || currentAlert.device_ip || '未知设备' }}
            </router-link>
            <span v-else>无</span>
          </a-descriptions-item>
          <a-descriptions-item label="告警时间">
            {{ formatDate(currentAlert.created_at) }}
          </a-descriptions-item>
          <a-descriptions-item label="持续时间">
            {{ getDuration(currentAlert) }}
          </a-descriptions-item>
          <a-descriptions-item label="确认时间">
            {{ currentAlert.acknowledged_at ? formatDate(currentAlert.acknowledged_at) : '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="解决时间">
            {{ currentAlert.resolved_at ? formatDate(currentAlert.resolved_at) : '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="告警消息" :span="2">
            {{ currentAlert.message }}
          </a-descriptions-item>
          <a-descriptions-item label="详细信息" :span="2">
            <pre style="margin: 0">{{ JSON.stringify(currentAlert.details || {}, null, 2) }}</pre>
          </a-descriptions-item>
        </a-descriptions>

        <!-- 操作历史 -->
        <a-divider>操作历史</a-divider>
        <a-timeline>
          <a-timeline-item color="green">
            <template #dot>
              <ClockCircleOutlined style="font-size: 16px" />
            </template>
            {{ formatDate(currentAlert.created_at) }} - 告警产生
          </a-timeline-item>
          <a-timeline-item v-if="currentAlert.acknowledged_at" color="blue">
            <template #dot>
              <CheckOutlined style="font-size: 16px" />
            </template>
            {{ formatDate(currentAlert.acknowledged_at) }} - 告警已确认
            <span v-if="currentAlert.acknowledged_by">（{{ currentAlert.acknowledged_by }}）</span>
          </a-timeline-item>
          <a-timeline-item v-if="currentAlert.resolved_at" color="green">
            <template #dot>
              <CheckCircleOutlined style="font-size: 16px" />
            </template>
            {{ formatDate(currentAlert.resolved_at) }} - 告警已解决
            <span v-if="currentAlert.resolved_by">（{{ currentAlert.resolved_by }}）</span>
          </a-timeline-item>
        </a-timeline>
      </template>

      <template #footer>
        <a-space>
          <a-button @click="detailVisible = false">关闭</a-button>
          <a-button
            type="primary"
            @click="acknowledgeAlert(currentAlert)"
            v-if="currentAlert?.status === 'active'"
          >
            确认告警
          </a-button>
          <a-button
            type="primary"
            @click="resolveAlert(currentAlert)"
            v-if="currentAlert?.status !== 'resolved'"
          >
            解决告警
          </a-button>
        </a-space>
      </template>
    </a-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  ReloadOutlined,
  ExportOutlined,
  BellOutlined,
  ExclamationCircleOutlined,
  QuestionCircleOutlined,
  CheckCircleOutlined,
  SearchOutlined,
  DownOutlined,
  EyeOutlined,
  CheckOutlined,
  StopOutlined,
  HistoryOutlined,
  LinkOutlined,
  ClockCircleOutlined
} from '@ant-design/icons-vue'
import { alertApi } from '@/api/alert'
import { deviceApi } from '@/api/device'
import { useAlertStore } from '@/stores/alert'
import { useWebSocketStore } from '@/stores/websocket'
import type { Alert } from '@/types/alert'
import type { Device } from '@/types/device'
import type { Dayjs } from 'dayjs'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import duration from 'dayjs/plugin/duration'
import 'dayjs/locale/zh-cn'
import { h } from 'vue'

dayjs.extend(relativeTime)
dayjs.extend(duration)
dayjs.locale('zh-cn')

const alertStore = useAlertStore()
const wsStore = useWebSocketStore()

// 状态
const loading = ref(false)
const statsLoading = ref(false)
const alerts = ref<Alert[]>([])
const devices = ref<Device[]>([])
const alertStats = ref({
  total: 0,
  critical: 0,
  unacknowledged: 0,
  resolved: 0
})

// 搜索和筛选
const searchText = ref('')
const filterSeverity = ref('')
const filterStatus = ref('')
const filterType = ref('')
const filterDevice = ref<number | undefined>()
const dateRange = ref<[Dayjs, Dayjs] | null>(null)

// 表格
const selectedRowKeys = ref<number[]>([])
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条`
})

// 详情抽屉
const detailVisible = ref(false)
const currentAlert = ref<Alert | null>(null)

// 表格列配置
const columns = [
  {
    title: '告警标题',
    dataIndex: 'title',
    key: 'title',
    slots: { customRender: 'title' },
    ellipsis: true
  },
  {
    title: '级别',
    dataIndex: 'severity',
    key: 'severity',
    slots: { customRender: 'severity' },
    width: 100,
    filters: [
      { text: '严重', value: 'critical' },
      { text: '高', value: 'high' },
      { text: '中', value: 'medium' },
      { text: '低', value: 'low' }
    ]
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    slots: { customRender: 'status' },
    width: 120
  },
  {
    title: '类型',
    dataIndex: 'alert_type',
    key: 'alert_type',
    width: 140
  },
  {
    title: '设备',
    dataIndex: 'device_name',
    key: 'device',
    slots: { customRender: 'device' },
    ellipsis: true
  },
  {
    title: '告警时间',
    dataIndex: 'created_at',
    key: 'created_at',
    slots: { customRender: 'created_at' },
    sorter: true,
    width: 120
  },
  {
    title: '持续时间',
    key: 'duration',
    slots: { customRender: 'duration' },
    width: 100
  },
  {
    title: '操作',
    key: 'action',
    slots: { customRender: 'action' },
    width: 100,
    fixed: 'right'
  }
]

// 自动刷新定时器
let refreshTimer: NodeJS.Timeout | null = null

// 计算属性
const filteredAlerts = computed(() => {
  let result = [...alerts.value]

  // 搜索过滤
  if (searchText.value) {
    const search = searchText.value.toLowerCase()
    result = result.filter(alert =>
      alert.title.toLowerCase().includes(search) ||
      alert.message.toLowerCase().includes(search)
    )
  }

  // 级别过滤
  if (filterSeverity.value) {
    result = result.filter(alert => alert.severity === filterSeverity.value)
  }

  // 状态过滤
  if (filterStatus.value) {
    result = result.filter(alert => alert.status === filterStatus.value)
  }

  // 类型过滤
  if (filterType.value) {
    result = result.filter(alert => alert.alert_type === filterType.value)
  }

  // 设备过滤
  if (filterDevice.value) {
    result = result.filter(alert => alert.device_id === filterDevice.value)
  }

  // 时间范围过滤
  if (dateRange.value) {
    const [start, end] = dateRange.value
    result = result.filter(alert => {
      const alertTime = dayjs(alert.created_at)
      return alertTime.isAfter(start) && alertTime.isBefore(end)
    })
  }

  pagination.value.total = result.length
  return result
})

// 方法
const fetchAlerts = async () => {
  loading.value = true
  try {
    const params: any = {
      skip: (pagination.value.current - 1) * pagination.value.pageSize,
      limit: pagination.value.pageSize
    }

    if (filterSeverity.value) params.severity = filterSeverity.value
    if (filterStatus.value) params.status = filterStatus.value
    if (filterDevice.value) params.device_id = filterDevice.value
    if (dateRange.value) {
      params.start_time = dateRange.value[0].format('YYYY-MM-DD HH:mm:ss')
      params.end_time = dateRange.value[1].format('YYYY-MM-DD HH:mm:ss')
    }

    const response = await alertApi.getAlerts(params)
    alerts.value = response.data.items || []
    pagination.value.total = response.data.total || 0
    alertStore.setAlerts(alerts.value)
  } catch (error: any) {
    message.error('获取告警列表失败: ' + (error.message || '未知错误'))
  } finally {
    loading.value = false
  }
}

const fetchAlertStats = async () => {
  statsLoading.value = true
  try {
    const response = await alertApi.getStatistics()
    alertStats.value = response.data
  } catch (error: any) {
    console.error('获取告警统计失败:', error)
  } finally {
    statsLoading.value = false
  }
}

const fetchDevices = async () => {
  try {
    const response = await deviceApi.getDevices()
    devices.value = response.data
  } catch (error: any) {
    console.error('获取设备列表失败:', error)
  }
}

const refreshAlerts = () => {
  fetchAlerts()
  fetchAlertStats()
}

const handleSearch = () => {
  pagination.value.current = 1
}

const handleFilter = () => {
  pagination.value.current = 1
  fetchAlerts()
}

const resetFilter = () => {
  searchText.value = ''
  filterSeverity.value = ''
  filterStatus.value = ''
  filterType.value = ''
  filterDevice.value = undefined
  dateRange.value = null
  handleFilter()
}

const handleTableChange = (paginationInfo: any, filters: any, sorter: any) => {
  pagination.value = paginationInfo
  fetchAlerts()
}

const onSelectChange = (keys: number[]) => {
  selectedRowKeys.value = keys
}

const clearSelection = () => {
  selectedRowKeys.value = []
}

const viewAlert = (alert: Alert) => {
  currentAlert.value = alert
  detailVisible.value = true
}

const handleMenuClick = async (key: string, alert: Alert) => {
  switch (key) {
    case 'view':
      viewAlert(alert)
      break
    case 'acknowledge':
      await acknowledgeAlert(alert)
      break
    case 'resolve':
      await resolveAlert(alert)
      break
    case 'suppress':
      await suppressAlert(alert)
      break
    case 'history':
      // TODO: 查看历史
      message.info('查看历史功能开发中')
      break
    case 'related':
      // TODO: 查看相关告警
      message.info('相关告警功能开发中')
      break
  }
}

const acknowledgeAlert = async (alert: Alert) => {
  try {
    await alertApi.acknowledgeAlert(alert.id)
    message.success('告警已确认')
    refreshAlerts()
    if (detailVisible.value && currentAlert.value?.id === alert.id) {
      // 更新当前详情
      currentAlert.value.status = 'acknowledged'
      currentAlert.value.acknowledged_at = new Date().toISOString()
    }
  } catch (error: any) {
    message.error('确认失败: ' + (error.message || '未知错误'))
  }
}

const resolveAlert = async (alert: Alert) => {
  try {
    await alertApi.resolveAlert(alert.id)
    message.success('告警已解决')
    refreshAlerts()
    if (detailVisible.value && currentAlert.value?.id === alert.id) {
      // 更新当前详情
      currentAlert.value.status = 'resolved'
      currentAlert.value.resolved_at = new Date().toISOString()
    }
  } catch (error: any) {
    message.error('解决失败: ' + (error.message || '未知错误'))
  }
}

const suppressAlert = async (alert: Alert) => {
  try {
    // TODO: 实现抑制告警API
    message.warning('抑制告警功能开发中')
  } catch (error: any) {
    message.error('抑制失败: ' + (error.message || '未知错误'))
  }
}

const batchAcknowledge = async () => {
  try {
    // TODO: 实现批量确认API
    message.info(`批量确认 ${selectedRowKeys.value.length} 条告警`)
    clearSelection()
  } catch (error: any) {
    message.error('批量确认失败: ' + (error.message || '未知错误'))
  }
}

const batchResolve = async () => {
  try {
    // TODO: 实现批量解决API
    message.info(`批量解决 ${selectedRowKeys.value.length} 条告警`)
    clearSelection()
  } catch (error: any) {
    message.error('批量解决失败: ' + (error.message || '未知错误'))
  }
}

const batchSuppress = async () => {
  try {
    // TODO: 实现批量抑制API
    message.info(`批量抑制 ${selectedRowKeys.value.length} 条告警`)
    clearSelection()
  } catch (error: any) {
    message.error('批量抑制失败: ' + (error.message || '未知错误'))
  }
}

const exportAlerts = () => {
  // TODO: 实现导出功能
  message.info('导出功能开发中')
}

const filterDeviceOption = (input: string, option: any) => {
  return option.children[0].children.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

// 工具函数
const getSeverityColor = (severity: string) => {
  const colors: Record<string, string> = {
    critical: 'red',
    high: 'orange',
    medium: 'gold',
    low: 'blue'
  }
  return colors[severity] || 'default'
}

const getSeverityText = (severity: string) => {
  const texts: Record<string, string> = {
    critical: '严重',
    high: '高',
    medium: '中',
    low: '低'
  }
  return texts[severity] || severity
}

const getStatusBadge = (status: string) => {
  const badges: Record<string, string> = {
    active: 'error',
    acknowledged: 'processing',
    resolved: 'success',
    suppressed: 'default'
  }
  return badges[status] || 'default'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    active: '活跃',
    acknowledged: '已确认',
    resolved: '已解决',
    suppressed: '已抑制'
  }
  return texts[status] || status
}

const getAlertTypeText = (type: string) => {
  const types: Record<string, string> = {
    device_down: '设备离线',
    high_cpu: 'CPU使用率高',
    high_memory: '内存使用率高',
    high_disk: '磁盘使用率高',
    network_anomaly: '网络异常',
    security_threat: '安全威胁',
    performance_degradation: '性能下降',
    configuration_change: '配置变更',
    other: '其他'
  }
  return types[type] || type
}

const formatDate = (date: string) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
}

const formatRelativeTime = (date: string) => {
  return dayjs(date).fromNow()
}

const getDuration = (alert: Alert) => {
  const start = dayjs(alert.created_at)
  const end = alert.resolved_at ? dayjs(alert.resolved_at) : dayjs()
  const diff = end.diff(start)
  
  if (diff < 60000) { // 小于1分钟
    return '< 1分钟'
  } else if (diff < 3600000) { // 小于1小时
    return Math.floor(diff / 60000) + '分钟'
  } else if (diff < 86400000) { // 小于1天
    return Math.floor(diff / 3600000) + '小时'
  } else {
    return Math.floor(diff / 86400000) + '天'
  }
}

// 生命周期
onMounted(() => {
  refreshAlerts()
  fetchDevices()

  // WebSocket连接
  if (!wsStore.connected) {
    wsStore.connect()
  }

  // 设置自动刷新
  refreshTimer = setInterval(() => {
    fetchAlerts()
    fetchAlertStats()
  }, 30000) // 30秒刷新一次
})

onUnmounted(() => {
  // 清理定时器
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
})
</script>

<style scoped lang="less">
.alert-list {
  padding: 16px;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    h1 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }

    .header-actions {
      display: flex;
      gap: 8px;
    }
  }

  .stats-row {
    margin-bottom: 16px;
  }

  .filter-card {
    margin-bottom: 16px;
  }

  .batch-actions {
    margin-bottom: 16px;
  }
}
</style>