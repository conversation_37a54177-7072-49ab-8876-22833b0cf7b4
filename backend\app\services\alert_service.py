"""
Alert management service
"""
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from loguru import logger

from ..models.alert import Alert, AlertType, AlertSeverity, AlertStatus
from ..models.device import Device


class AlertService:
    """Alert management and notification service"""
    
    def __init__(self):
        self.alert_rules = self._load_default_alert_rules()
    
    def _load_default_alert_rules(self) -> Dict[str, Dict[str, Any]]:
        """Load default alert rules configuration"""
        return {
            'cpu_usage': {
                'warning_threshold': 70,
                'critical_threshold': 90,
                'check_interval': 300,  # 5 minutes
                'escalation_time': 1800  # 30 minutes
            },
            'memory_usage': {
                'warning_threshold': 80,
                'critical_threshold': 95,
                'check_interval': 300,
                'escalation_time': 1800
            },
            'disk_usage': {
                'warning_threshold': 85,
                'critical_threshold': 95,
                'check_interval': 600,  # 10 minutes
                'escalation_time': 3600  # 1 hour
            },
            'device_offline': {
                'check_interval': 60,  # 1 minute
                'escalation_time': 300  # 5 minutes
            },
            'interface_down': {
                'check_interval': 120,  # 2 minutes
                'escalation_time': 600  # 10 minutes
            }
        }
    
    async def create_alert(
        self,
        title: str,
        description: str,
        alert_type: AlertType,
        severity: AlertSeverity,
        device_id: Optional[int] = None,
        device_ip: Optional[str] = None,
        source: str = "System",
        threshold_value: Optional[str] = None,
        current_value: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        db: Session = None
    ) -> Alert:
        """Create new alert"""
        try:
            # Check for existing similar alert to avoid duplicates
            existing_alert = None
            if device_id:
                existing_alert = db.query(Alert).filter(
                    Alert.device_id == device_id,
                    Alert.alert_type == alert_type,
                    Alert.status.in_([AlertStatus.OPEN, AlertStatus.ACKNOWLEDGED]),
                    Alert.is_active == True
                ).first()
            
            if existing_alert:
                # Update existing alert
                existing_alert.last_occurrence = datetime.now().isoformat()
                existing_alert.current_value = current_value
                existing_alert.metadata = metadata
                db.commit()
                
                logger.info(f"Updated existing alert: {existing_alert.id}")
                return existing_alert
            
            # Create new alert
            alert = Alert(
                title=title,
                description=description,
                alert_type=alert_type,
                severity=severity,
                status=AlertStatus.OPEN,
                device_id=device_id,
                device_ip=device_ip,
                source=source,
                threshold_value=threshold_value,
                current_value=current_value,
                first_occurrence=datetime.now().isoformat(),
                last_occurrence=datetime.now().isoformat(),
                metadata=metadata or {}
            )
            
            db.add(alert)
            db.commit()
            db.refresh(alert)
            
            logger.info(f"Created new alert: {alert.id} - {title}")
            
            # TODO: Send notifications (email, webhook, etc.)
            await self._send_alert_notification(alert)
            
            return alert
            
        except Exception as e:
            logger.error(f"Error creating alert: {e}")
            if db:
                db.rollback()
            raise
    
    async def _send_alert_notification(self, alert: Alert):
        """Send alert notification via WebSocket and other channels"""
        try:
            # Import here to avoid circular imports
            from ..services.websocket_service import websocket_service
            
            # Prepare alert data for notification
            alert_data = {
                "id": alert.id,
                "title": alert.title,
                "description": alert.description,
                "severity": alert.severity.value,
                "alert_type": alert.alert_type.value,
                "device_id": alert.device_id,
                "device_ip": alert.device_ip,
                "created_at": alert.created_at.isoformat() if hasattr(alert.created_at, 'isoformat') else str(alert.created_at),
                "status": alert.status.value
            }
            
            # Send WebSocket notification
            await websocket_service.broadcast_alert(alert_data)
            
            # TODO: Add other notification channels
            # - Email notifications
            # - SMS notifications  
            # - Webhook notifications
            # - Slack/Teams integration
            
            logger.info(f"Alert notification sent for alert {alert.id}: {alert.title}")
            
        except Exception as e:
            logger.error(f"Error sending alert notification for alert {alert.id}: {e}")
    
    async def acknowledge_alert(self, alert_id: int, acknowledged_by: str, db: Session) -> bool:
        """Acknowledge an alert"""
        try:
            alert = db.query(Alert).filter(
                Alert.id == alert_id,
                Alert.is_active == True
            ).first()
            
            if not alert:
                return False
            
            alert.status = AlertStatus.ACKNOWLEDGED
            alert.acknowledged_at = datetime.now().isoformat()
            alert.acknowledged_by = acknowledged_by
            
            db.commit()
            
            logger.info(f"Alert {alert_id} acknowledged by {acknowledged_by}")
            return True
            
        except Exception as e:
            logger.error(f"Error acknowledging alert {alert_id}: {e}")
            db.rollback()
            return False
    
    async def resolve_alert(
        self, 
        alert_id: int, 
        resolved_by: str, 
        resolution_notes: str = "",
        db: Session = None
    ) -> bool:
        """Resolve an alert"""
        try:
            alert = db.query(Alert).filter(
                Alert.id == alert_id,
                Alert.is_active == True
            ).first()
            
            if not alert:
                return False
            
            alert.status = AlertStatus.RESOLVED
            alert.resolved_at = datetime.now().isoformat()
            alert.resolved_by = resolved_by
            alert.resolution_notes = resolution_notes
            
            db.commit()
            
            logger.info(f"Alert {alert_id} resolved by {resolved_by}")
            return True
            
        except Exception as e:
            logger.error(f"Error resolving alert {alert_id}: {e}")
            db.rollback()
            return False
    
    async def auto_resolve_alerts(self, device_id: int, alert_types: List[AlertType], db: Session):
        """Auto-resolve alerts when conditions are no longer met"""
        try:
            # Find open alerts for the device and alert types
            alerts_to_resolve = db.query(Alert).filter(
                Alert.device_id == device_id,
                Alert.alert_type.in_(alert_types),
                Alert.status.in_([AlertStatus.OPEN, AlertStatus.ACKNOWLEDGED]),
                Alert.is_active == True
            ).all()
            
            for alert in alerts_to_resolve:
                alert.status = AlertStatus.RESOLVED
                alert.resolved_at = datetime.now().isoformat()
                alert.resolved_by = "System"
                alert.resolution_notes = "Auto-resolved: Condition no longer met"
            
            if alerts_to_resolve:
                db.commit()
                logger.info(f"Auto-resolved {len(alerts_to_resolve)} alerts for device {device_id}")
            
        except Exception as e:
            logger.error(f"Error auto-resolving alerts for device {device_id}: {e}")
            db.rollback()
    
    async def escalate_alerts(self, db: Session):
        """Escalate alerts that have been open for too long"""
        try:
            current_time = datetime.now()
            escalated_count = 0
            
            # Get alerts that need escalation
            for alert_type, rules in self.alert_rules.items():
                escalation_time = rules.get('escalation_time', 1800)  # Default 30 minutes
                cutoff_time = current_time - timedelta(seconds=escalation_time)
                
                alerts_to_escalate = db.query(Alert).filter(
                    Alert.status == AlertStatus.OPEN,
                    Alert.severity != AlertSeverity.CRITICAL,
                    Alert.first_occurrence <= cutoff_time.isoformat(),
                    Alert.is_active == True
                ).all()
                
                for alert in alerts_to_escalate:
                    # Escalate severity
                    if alert.severity == AlertSeverity.LOW:
                        alert.severity = AlertSeverity.MEDIUM
                    elif alert.severity == AlertSeverity.MEDIUM:
                        alert.severity = AlertSeverity.HIGH
                    elif alert.severity == AlertSeverity.HIGH:
                        alert.severity = AlertSeverity.CRITICAL
                    
                    # Add escalation note
                    if not alert.metadata:
                        alert.metadata = {}
                    alert.metadata['escalated_at'] = current_time.isoformat()
                    alert.metadata['escalation_reason'] = f"Alert open for more than {escalation_time} seconds"
                    
                    escalated_count += 1
            
            if escalated_count > 0:
                db.commit()
                logger.info(f"Escalated {escalated_count} alerts")
            
        except Exception as e:
            logger.error(f"Error escalating alerts: {e}")
            db.rollback()
    
    async def get_alert_statistics(self, db: Session, hours: int = 24) -> Dict[str, Any]:
        """Get alert statistics for the specified time period"""
        try:
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=hours)
            
            # Get alerts in time range
            alerts = db.query(Alert).filter(
                Alert.created_at >= start_time,
                Alert.created_at <= end_time,
                Alert.is_active == True
            ).all()
            
            # Calculate statistics
            stats = {
                'total_alerts': len(alerts),
                'by_severity': {
                    'low': len([a for a in alerts if a.severity == AlertSeverity.LOW]),
                    'medium': len([a for a in alerts if a.severity == AlertSeverity.MEDIUM]),
                    'high': len([a for a in alerts if a.severity == AlertSeverity.HIGH]),
                    'critical': len([a for a in alerts if a.severity == AlertSeverity.CRITICAL])
                },
                'by_status': {
                    'open': len([a for a in alerts if a.status == AlertStatus.OPEN]),
                    'acknowledged': len([a for a in alerts if a.status == AlertStatus.ACKNOWLEDGED]),
                    'resolved': len([a for a in alerts if a.status == AlertStatus.RESOLVED]),
                    'closed': len([a for a in alerts if a.status == AlertStatus.CLOSED])
                },
                'by_type': {},
                'top_devices': {},
                'time_range': {
                    'start': start_time.isoformat(),
                    'end': end_time.isoformat(),
                    'hours': hours
                }
            }
            
            # Count by alert type
            for alert in alerts:
                alert_type = alert.alert_type.value
                stats['by_type'][alert_type] = stats['by_type'].get(alert_type, 0) + 1
            
            # Count by device
            for alert in alerts:
                if alert.device_id:
                    device_id = str(alert.device_id)
                    stats['top_devices'][device_id] = stats['top_devices'].get(device_id, 0) + 1
            
            # Sort top devices by alert count
            stats['top_devices'] = dict(
                sorted(stats['top_devices'].items(), key=lambda x: x[1], reverse=True)[:10]
            )
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting alert statistics: {e}")
            return {
                'error': str(e),
                'total_alerts': 0
            }
    
    async def cleanup_old_alerts(self, db: Session, days: int = 30):
        """Clean up old resolved alerts"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            
            # Soft delete old resolved alerts
            old_alerts = db.query(Alert).filter(
                Alert.status == AlertStatus.RESOLVED,
                Alert.resolved_at <= cutoff_date.isoformat(),
                Alert.is_active == True
            ).all()
            
            for alert in old_alerts:
                alert.is_active = False
            
            if old_alerts:
                db.commit()
                logger.info(f"Cleaned up {len(old_alerts)} old alerts")
            
        except Exception as e:
            logger.error(f"Error cleaning up old alerts: {e}")
            db.rollback()
