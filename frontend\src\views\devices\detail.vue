<template>
  <div class="device-detail">
    <!-- 返回按钮 -->
    <div class="page-header">
      <a-button @click="goBack" :icon="h(ArrowLeftOutlined)">返回设备列表</a-button>
    </div>

    <!-- 设备基本信息 -->
    <a-card title="设备信息" :loading="loading" :bordered="false" style="margin-bottom: 16px">
      <template v-if="device">
        <a-descriptions :column="{ xs: 1, sm: 2, md: 3 }" bordered>
          <a-descriptions-item label="设备名称">
            {{ device.name }}
          </a-descriptions-item>
          <a-descriptions-item label="IP地址">
            {{ device.ip_address }}
          </a-descriptions-item>
          <a-descriptions-item label="MAC地址">
            {{ device.mac_address || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="设备类型">
            <component :is="getDeviceIcon(device.device_type)" />
            {{ getDeviceTypeText(device.device_type) }}
          </a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag :color="getStatusColor(device.status)">
              {{ getStatusText(device.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="健康评分">
            <a-progress
              :percent="device.health_score"
              :stroke-color="getHealthColor(device.health_score)"
              :format="percent => `${percent}%`"
            />
          </a-descriptions-item>
          <a-descriptions-item label="厂商">
            {{ device.vendor || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="型号">
            {{ device.model || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="位置">
            {{ device.location || '-' }}
            {{ device.building ? `- ${device.building}` : '' }}
            {{ device.floor ? `- ${device.floor}` : '' }}
            {{ device.room ? `- ${device.room}` : '' }}
          </a-descriptions-item>
          <a-descriptions-item label="子网">
            {{ device.subnet || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="VLAN ID">
            {{ device.vlan_id || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="最后上线时间">
            <a-tooltip :title="formatDate(device.last_seen)">
              {{ formatRelativeTime(device.last_seen) }}
            </a-tooltip>
          </a-descriptions-item>
          <a-descriptions-item label="创建时间" :span="3">
            {{ formatDate(device.created_at) }}
          </a-descriptions-item>
          <a-descriptions-item label="描述" :span="3">
            {{ device.description || '-' }}
          </a-descriptions-item>
        </a-descriptions>

        <!-- 操作按钮 -->
        <div class="action-buttons" style="margin-top: 16px">
          <a-space>
            <a-button type="primary" @click="editDevice" :icon="h(EditOutlined)">
              编辑设备
            </a-button>
            <a-button @click="testConnection" :loading="testing" :icon="h(ApiOutlined)">
              测试连接
            </a-button>
            <a-button @click="collectMetrics" :loading="collecting" :icon="h(SyncOutlined)">
              收集指标
            </a-button>
            <a-button danger @click="deleteDevice" :icon="h(DeleteOutlined)">
              删除设备
            </a-button>
          </a-space>
        </div>
      </template>
    </a-card>

    <!-- 实时性能指标 -->
    <a-card title="实时性能" :bordered="false" style="margin-bottom: 16px">
      <a-row :gutter="16">
        <a-col :xs="24" :sm="8">
          <div class="metric-card">
            <div class="metric-label">CPU使用率</div>
            <a-progress
              type="dashboard"
              :percent="device?.cpu_usage || 0"
              :stroke-color="getMetricColor(device?.cpu_usage || 0)"
            />
          </div>
        </a-col>
        <a-col :xs="24" :sm="8">
          <div class="metric-card">
            <div class="metric-label">内存使用率</div>
            <a-progress
              type="dashboard"
              :percent="device?.memory_usage || 0"
              :stroke-color="getMetricColor(device?.memory_usage || 0)"
            />
          </div>
        </a-col>
        <a-col :xs="24" :sm="8">
          <div class="metric-card">
            <div class="metric-label">磁盘使用率</div>
            <a-progress
              type="dashboard"
              :percent="device?.disk_usage || 0"
              :stroke-color="getMetricColor(device?.disk_usage || 0)"
            />
          </div>
        </a-col>
      </a-row>

      <!-- 刷新按钮 -->
      <div style="text-align: center; margin-top: 16px">
        <a-button @click="refreshMetrics" :loading="metricsLoading" :icon="h(ReloadOutlined)">
          刷新性能数据
        </a-button>
      </div>
    </a-card>

    <!-- 性能历史图表 -->
    <a-card title="性能历史" :bordered="false" style="margin-bottom: 16px">
      <div class="charts-container">
        <div ref="cpuChartRef" style="height: 300px"></div>
        <div ref="memoryChartRef" style="height: 300px; margin-top: 16px"></div>
        <div ref="networkChartRef" style="height: 300px; margin-top: 16px"></div>
      </div>
    </a-card>

    <!-- 最近告警 -->
    <a-card title="最近告警" :bordered="false" style="margin-bottom: 16px">
      <a-table
        :columns="alertColumns"
        :data-source="recentAlerts"
        :loading="alertsLoading"
        :pagination="{ pageSize: 5 }"
        size="small"
      >
        <template #severity="{ record }">
          <a-tag :color="getAlertColor(record.severity)">
            {{ record.severity }}
          </a-tag>
        </template>
        <template #created_at="{ record }">
          {{ formatDate(record.created_at) }}
        </template>
        <template #action="{ record }">
          <router-link :to="`/alerts?device_id=${device?.id}`">查看详情</router-link>
        </template>
      </a-table>
    </a-card>

    <!-- SNMP信息 -->
    <a-card title="SNMP配置" :bordered="false" v-if="device?.snmp_enabled">
      <a-descriptions :column="2" bordered>
        <a-descriptions-item label="SNMP版本">
          {{ device.snmp_version || '2c' }}
        </a-descriptions-item>
        <a-descriptions-item label="SNMP端口">
          {{ device.snmp_port || 161 }}
        </a-descriptions-item>
        <a-descriptions-item label="团体名" :span="2">
          <span style="filter: blur(5px)">{{ device.snmp_community || 'public' }}</span>
          <a-button type="link" size="small" @click="showSnmpCommunity = !showSnmpCommunity">
            {{ showSnmpCommunity ? '隐藏' : '显示' }}
          </a-button>
        </a-descriptions-item>
      </a-descriptions>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, h, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message, Modal } from 'ant-design-vue'
import * as echarts from 'echarts'
import {
  ArrowLeftOutlined,
  EditOutlined,
  ApiOutlined,
  SyncOutlined,
  DeleteOutlined,
  ReloadOutlined,
  DesktopOutlined,
  ClusterOutlined,
  CloudServerOutlined,
  SafetyCertificateOutlined,
  WifiOutlined
} from '@ant-design/icons-vue'
import { deviceApi } from '@/api/device'
import { alertApi } from '@/api/alert'
import { performanceApi } from '@/api/performance'
import type { Device } from '@/types/device'
import type { Alert } from '@/types/alert'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/zh-cn'

dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

const route = useRoute()
const router = useRouter()

// 状态
const loading = ref(false)
const device = ref<Device | null>(null)
const testing = ref(false)
const collecting = ref(false)
const metricsLoading = ref(false)
const alertsLoading = ref(false)
const recentAlerts = ref<Alert[]>([])
const showSnmpCommunity = ref(false)

// 图表实例
const cpuChartRef = ref<HTMLDivElement>()
const memoryChartRef = ref<HTMLDivElement>()
const networkChartRef = ref<HTMLDivElement>()
let cpuChart: echarts.ECharts | null = null
let memoryChart: echarts.ECharts | null = null
let networkChart: echarts.ECharts | null = null

// 告警表格列
const alertColumns = [
  {
    title: '告警标题',
    dataIndex: 'title',
    key: 'title'
  },
  {
    title: '级别',
    dataIndex: 'severity',
    key: 'severity',
    slots: { customRender: 'severity' }
  },
  {
    title: '类型',
    dataIndex: 'alert_type',
    key: 'alert_type'
  },
  {
    title: '时间',
    dataIndex: 'created_at',
    key: 'created_at',
    slots: { customRender: 'created_at' }
  },
  {
    title: '操作',
    key: 'action',
    slots: { customRender: 'action' }
  }
]

// 自动刷新定时器
let refreshTimer: NodeJS.Timeout | null = null

// 方法
const fetchDevice = async () => {
  const deviceId = Number(route.params.id)
  if (!deviceId) {
    message.error('无效的设备ID')
    router.push('/devices')
    return
  }

  loading.value = true
  try {
    const response = await deviceApi.getDevice(deviceId)
    device.value = response.data
  } catch (error: any) {
    message.error('获取设备详情失败: ' + (error.message || '未知错误'))
    router.push('/devices')
  } finally {
    loading.value = false
  }
}

const fetchRecentAlerts = async () => {
  if (!device.value) return

  alertsLoading.value = true
  try {
    const response = await alertApi.getAlerts({
      device_id: device.value.id,
      limit: 5
    })
    recentAlerts.value = response.data.items || []
  } catch (error: any) {
    console.error('获取告警失败:', error)
  } finally {
    alertsLoading.value = false
  }
}

const fetchPerformanceHistory = async () => {
  if (!device.value) return

  try {
    const response = await performanceApi.getDeviceMetrics(device.value.id, {
      time_range: '24h'
    })
    const data = response.data

    // 更新图表数据
    updateCharts(data)
  } catch (error: any) {
    console.error('获取性能历史失败:', error)
  }
}

const initCharts = () => {
  if (cpuChartRef.value) {
    cpuChart = echarts.init(cpuChartRef.value)
    cpuChart.setOption(getCpuChartOption())
  }

  if (memoryChartRef.value) {
    memoryChart = echarts.init(memoryChartRef.value)
    memoryChart.setOption(getMemoryChartOption())
  }

  if (networkChartRef.value) {
    networkChart = echarts.init(networkChartRef.value)
    networkChart.setOption(getNetworkChartOption())
  }

  // 响应式处理
  window.addEventListener('resize', handleResize)
}

const getCpuChartOption = () => ({
  title: {
    text: 'CPU使用率',
    left: 'center'
  },
  tooltip: {
    trigger: 'axis'
  },
  xAxis: {
    type: 'time',
    boundaryGap: false
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: '{value}%'
    }
  },
  series: [
    {
      name: 'CPU使用率',
      type: 'line',
      smooth: true,
      symbol: 'none',
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: '#1890ff' },
          { offset: 1, color: '#e6f7ff' }
        ])
      },
      data: []
    }
  ]
})

const getMemoryChartOption = () => ({
  title: {
    text: '内存使用率',
    left: 'center'
  },
  tooltip: {
    trigger: 'axis'
  },
  xAxis: {
    type: 'time',
    boundaryGap: false
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: '{value}%'
    }
  },
  series: [
    {
      name: '内存使用率',
      type: 'line',
      smooth: true,
      symbol: 'none',
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: '#52c41a' },
          { offset: 1, color: '#f6ffed' }
        ])
      },
      data: []
    }
  ]
})

const getNetworkChartOption = () => ({
  title: {
    text: '网络流量',
    left: 'center'
  },
  tooltip: {
    trigger: 'axis'
  },
  legend: {
    data: ['入站流量', '出站流量'],
    bottom: 0
  },
  xAxis: {
    type: 'time',
    boundaryGap: false
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: (value: number) => {
        if (value >= 1e9) return (value / 1e9).toFixed(1) + ' GB/s'
        if (value >= 1e6) return (value / 1e6).toFixed(1) + ' MB/s'
        if (value >= 1e3) return (value / 1e3).toFixed(1) + ' KB/s'
        return value + ' B/s'
      }
    }
  },
  series: [
    {
      name: '入站流量',
      type: 'line',
      smooth: true,
      symbol: 'none',
      data: []
    },
    {
      name: '出站流量',
      type: 'line',
      smooth: true,
      symbol: 'none',
      data: []
    }
  ]
})

const updateCharts = (data: any) => {
  // 处理性能数据并更新图表
  // 这里需要根据实际API返回的数据格式进行处理
  console.log('更新图表数据:', data)
}

const handleResize = () => {
  cpuChart?.resize()
  memoryChart?.resize()
  networkChart?.resize()
}

const goBack = () => {
  router.push('/devices')
}

const editDevice = () => {
  // TODO: 实现编辑功能，可以打开一个模态框或跳转到编辑页面
  message.info('编辑功能开发中')
}

const testConnection = async () => {
  if (!device.value) return

  testing.value = true
  try {
    const response = await deviceApi.testConnection(device.value.id)
    if (response.data.success) {
      message.success('连接测试成功')
    } else {
      message.error('连接测试失败: ' + response.data.message)
    }
  } catch (error: any) {
    message.error('测试失败: ' + (error.message || '未知错误'))
  } finally {
    testing.value = false
  }
}

const collectMetrics = async () => {
  if (!device.value) return

  collecting.value = true
  try {
    await deviceApi.collectMetrics(device.value.id)
    message.success('已开始收集性能指标')
    
    // 3秒后刷新数据
    setTimeout(() => {
      refreshMetrics()
      fetchPerformanceHistory()
    }, 3000)
  } catch (error: any) {
    message.error('收集失败: ' + (error.message || '未知错误'))
  } finally {
    collecting.value = false
  }
}

const refreshMetrics = async () => {
  metricsLoading.value = true
  await fetchDevice()
  metricsLoading.value = false
}

const deleteDevice = () => {
  if (!device.value) return

  Modal.confirm({
    title: '确认删除',
    content: `确定要删除设备 "${device.value.name}" 吗？此操作不可恢复。`,
    okText: '确定',
    cancelText: '取消',
    okType: 'danger',
    onOk: async () => {
      try {
        await deviceApi.deleteDevice(device.value!.id)
        message.success('设备删除成功')
        router.push('/devices')
      } catch (error: any) {
        message.error('删除失败: ' + (error.message || '未知错误'))
      }
    }
  })
}

// 工具函数
const getStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    online: 'success',
    offline: 'error',
    warning: 'warning',
    critical: 'error',
    maintenance: 'default',
    unknown: 'default'
  }
  return colors[status] || 'default'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    online: '在线',
    offline: '离线',
    warning: '警告',
    critical: '严重',
    maintenance: '维护中',
    unknown: '未知'
  }
  return texts[status] || status
}

const getDeviceTypeText = (type: string) => {
  const types: Record<string, string> = {
    switch: '交换机',
    router: '路由器',
    firewall: '防火墙',
    server: '服务器',
    ap: '无线AP',
    other: '其他'
  }
  return types[type] || type
}

const getDeviceIcon = (type: string) => {
  const icons: Record<string, any> = {
    switch: ClusterOutlined,
    router: CloudServerOutlined,
    firewall: SafetyCertificateOutlined,
    server: DesktopOutlined,
    ap: WifiOutlined,
    other: DesktopOutlined
  }
  return icons[type] || DesktopOutlined
}

const getHealthColor = (score: number) => {
  if (score >= 80) return '#52c41a'
  if (score >= 60) return '#faad14'
  return '#ff4d4f'
}

const getMetricColor = (value: number) => {
  if (value <= 60) return '#52c41a'
  if (value <= 80) return '#faad14'
  return '#ff4d4f'
}

const getAlertColor = (severity: string) => {
  const colors: Record<string, string> = {
    low: 'blue',
    medium: 'gold',
    high: 'orange',
    critical: 'red'
  }
  return colors[severity] || 'default'
}

const formatDate = (date: string) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
}

const formatRelativeTime = (date: string) => {
  return dayjs(date).fromNow()
}

// 监听设备变化，重新获取数据
watch(() => device.value, (newDevice) => {
  if (newDevice) {
    fetchRecentAlerts()
    fetchPerformanceHistory()
  }
})

// 生命周期
onMounted(() => {
  fetchDevice()
  
  // 初始化图表
  setTimeout(() => {
    initCharts()
  }, 100)

  // 设置自动刷新
  refreshTimer = setInterval(() => {
    refreshMetrics()
  }, 30000) // 30秒刷新一次
})

onUnmounted(() => {
  // 清理定时器
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }

  // 清理图表
  cpuChart?.dispose()
  memoryChart?.dispose()
  networkChart?.dispose()

  // 移除事件监听
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped lang="less">
.device-detail {
  padding: 16px;

  .page-header {
    margin-bottom: 16px;
  }

  .action-buttons {
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
  }

  .metric-card {
    text-align: center;
    padding: 16px;

    .metric-label {
      font-size: 14px;
      color: #666;
      margin-bottom: 16px;
    }
  }

  .charts-container {
    padding: 16px 0;
  }
}
</style>