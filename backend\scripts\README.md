# CampusGuard 数据库管理脚本说明

本目录包含了CampusGuard项目的数据库管理和维护脚本。

## 脚本列表

### 1. `init_production_db.sh` - 生产环境数据库初始化
**用途**: 在生产环境中安全地初始化数据库、创建用户、设置权限

**使用方法**:
```bash
# 基本使用
./init_production_db.sh campusguard campusguard your_password

# 使用默认数据库名和用户名
./init_production_db.sh "" "" your_password
```

**功能**:
- 创建数据库和用户
- 设置适当的权限
- 生成安全的配置文件
- 初始化Alembic迁移
- 设置自动备份
- 创建必要的目录

### 2. `db_health_check.py` - 数据库健康检查
**用途**: 全面检查数据库状态、性能和数据完整性

**使用方法**:
```bash
# 基本健康检查
python db_health_check.py

# 详细输出
python db_health_check.py --verbose

# 输出到JSON文件
python db_health_check.py --format json --output health_report.json

# 仅文本输出到文件
python db_health_check.py --output health_report.txt
```

**检查项目**:
- 数据库连接状态
- 表结构完整性
- 数据一致性
- 性能指标
- 存储空间
- 备份状态
- 迁移状态

**退出码**:
- 0: 健康
- 1: 警告
- 2: 严重问题

### 3. `db_maintenance.py` - 数据库维护
**用途**: 定期清理旧数据、优化表结构、更新统计信息

**使用方法**:
```bash
# 预览维护操作（推荐先运行）
python db_maintenance.py --dry-run

# 执行完整维护
python db_maintenance.py

# 自定义清理时间
python db_maintenance.py --cleanup-metrics-days 15 --cleanup-alerts-days 60

# 仅清理数据
python db_maintenance.py --cleanup-only

# 仅优化表
python db_maintenance.py --optimize-only

# 跳过表优化
python db_maintenance.py --skip-optimization

# 生成详细报告
python db_maintenance.py --verbose --output maintenance_report.txt
```

**维护操作**:
- 清理旧性能指标（默认30天前）
- 清理已解决的告警（默认90天前）
- 清理旧AI对话记录（默认180天前）
- 清理孤立数据
- 优化表结构
- 更新统计信息
- 检查和修复表

## 设置可执行权限

```bash
# 进入脚本目录
cd backend/scripts

# 设置权限
chmod +x init_production_db.sh
chmod +x db_health_check.py
chmod +x db_maintenance.py
```

## 定时任务设置

### 1. 自动备份（已在init_production_db.sh中设置）
```bash
# 查看当前定时任务
crontab -l

# 手动添加备份任务（如果需要）
echo "0 2 * * * /usr/local/bin/campusguard_backup.sh" | crontab -
```

### 2. 健康检查
```bash
# 每小时检查一次，有问题时发送邮件
0 * * * * cd /path/to/backend && python scripts/db_health_check.py || echo "数据库健康检查失败" | mail -s "CampusGuard DB Alert" <EMAIL>
```

### 3. 定期维护
```bash
# 每周日凌晨3点执行维护
0 3 * * 0 cd /path/to/backend && python scripts/db_maintenance.py --output /var/log/campusguard/maintenance_$(date +\%Y\%m\%d).log
```

## 监控集成

### 1. Nagios/Zabbix 集成
```bash
# 创建监控脚本
cat > /usr/local/bin/check_campusguard_db << 'EOF'
#!/bin/bash
cd /path/to/backend
python scripts/db_health_check.py --format json --output /tmp/db_health.json
exit_code=$?

if [ $exit_code -eq 0 ]; then
    echo "OK - Database is healthy"
elif [ $exit_code -eq 1 ]; then
    echo "WARNING - Database has warnings"
else
    echo "CRITICAL - Database has critical issues"
fi

exit $exit_code
EOF

chmod +x /usr/local/bin/check_campusguard_db
```

### 2. Prometheus 集成
```bash
# 创建指标导出脚本
cat > /usr/local/bin/export_campusguard_metrics << 'EOF'
#!/bin/bash
cd /path/to/backend
python scripts/db_health_check.py --format json | jq -r '
.checks | to_entries[] | 
"campusguard_db_check{check=\"" + .key + "\",status=\"" + .value.status + "\"} " + 
(if .value.status == "PASS" then "1" else "0" end)
' > /var/lib/node_exporter/textfile_collector/campusguard_db.prom
EOF

chmod +x /usr/local/bin/export_campusguard_metrics

# 添加到定时任务
echo "*/5 * * * * /usr/local/bin/export_campusguard_metrics" | crontab -
```

## 故障排除

### 常见问题

#### 1. 权限错误
```bash
# 检查文件权限
ls -la scripts/

# 重新设置权限
chmod +x scripts/*.sh scripts/*.py

# 检查数据库权限
mysql -u campusguard -p -e "SHOW GRANTS;"
```

#### 2. 连接失败
```bash
# 检查数据库服务
systemctl status mysql

# 检查配置文件
cat config/.env | grep DATABASE

# 测试连接
mysql -u campusguard -p campusguard -e "SELECT 1;"
```

#### 3. 脚本执行失败
```bash
# 检查Python环境
python --version
pip list | grep -E "(sqlalchemy|pymysql|loguru)"

# 检查日志
tail -f /var/log/campusguard/campusguard.log

# 手动执行调试
python scripts/db_health_check.py --verbose
```

### 日志位置

- 应用日志: `/var/log/campusguard/campusguard.log`
- 备份日志: `/var/backups/campusguard/`
- 维护报告: 通过 `--output` 参数指定
- MySQL日志: `/var/log/mysql/error.log`

## 最佳实践

### 1. 生产环境部署
1. 先在测试环境验证所有脚本
2. 使用 `--dry-run` 预览维护操作
3. 在维护窗口执行数据库操作
4. 始终在操作前备份数据库

### 2. 监控设置
1. 设置健康检查告警
2. 监控备份状态
3. 跟踪维护操作结果
4. 定期检查磁盘空间

### 3. 安全考虑
1. 限制脚本执行权限
2. 保护配置文件安全
3. 定期轮换数据库密码
4. 加密备份文件

### 4. 性能优化
1. 在低峰期执行维护
2. 监控维护操作的性能影响
3. 根据数据增长调整清理策略
4. 定期检查索引使用情况

## 联系支持

如果遇到问题，请：
1. 检查日志文件
2. 运行健康检查脚本
3. 收集错误信息
4. 联系开发团队

---

**最后更新**: 2025-01-01  
**版本**: 1.0.0  
**维护者**: CampusGuard开发团队