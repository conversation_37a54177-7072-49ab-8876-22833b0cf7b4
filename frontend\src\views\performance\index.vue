<template>
  <div class="performance-monitoring">
    <a-page-header
      title="性能监控"
      sub-title="网络设备性能数据监控和分析"
    >
      <template #extra>
        <a-space>
          <a-button @click="showDetailedMetrics" type="primary">
            <template #icon><LineChartOutlined /></template>
            详细指标
          </a-button>
          <a-button @click="refreshData" :loading="loading">
            <template #icon><ReloadOutlined /></template>
            刷新数据
          </a-button>
          <a-button @click="exportData">
            <template #icon><DownloadOutlined /></template>
            导出数据
          </a-button>
          <a-switch
            v-model:checked="autoRefresh"
            checked-children="自动刷新"
            un-checked-children="手动刷新"
            @change="toggleAutoRefresh"
          />
        </a-space>
      </template>
    </a-page-header>

    <!-- 设备选择和时间范围 -->
    <a-card class="filter-card">
      <a-form layout="inline" :model="filters">
        <a-form-item label="设备">
          <a-select
            v-model:value="filters.deviceId"
            placeholder="选择设备"
            style="width: 200px"
            allowClear
            show-search
            :filter-option="filterDeviceOption"
            @change="loadPerformanceData"
          >
            <a-select-option
              v-for="device in devices"
              :key="device.id"
              :value="device.id"
            >
              {{ device.name }} ({{ device.ip_address }})
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="时间范围">
          <a-select
            v-model:value="filters.timeRange"
            style="width: 150px"
            @change="loadPerformanceData"
          >
            <a-select-option value="1h">最近1小时</a-select-option>
            <a-select-option value="6h">最近6小时</a-select-option>
            <a-select-option value="24h">最近24小时</a-select-option>
            <a-select-option value="7d">最近7天</a-select-option>
            <a-select-option value="30d">最近30天</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="指标类型">
          <a-checkbox-group
            v-model:value="filters.metricTypes"
            @change="loadPerformanceData"
          >
            <a-checkbox value="cpu">CPU使用率</a-checkbox>
            <a-checkbox value="memory">内存使用率</a-checkbox>
            <a-checkbox value="network">网络流量</a-checkbox>
            <a-checkbox value="disk">磁盘使用率</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 性能概览卡片 -->
    <a-row :gutter="16" class="stats-row">
      <a-col :xs="24" :sm="12" :md="6">
        <a-card>
          <a-statistic
            title="平均CPU使用率"
            :value="performanceStats.avgCpu"
            suffix="%"
            :precision="1"
            :loading="loading"
          >
            <template #prefix>
              <ProcessorOutlined style="color: #1890ff" />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :xs="24" :sm="12" :md="6">
        <a-card>
          <a-statistic
            title="平均内存使用率"
            :value="performanceStats.avgMemory"
            suffix="%"
            :precision="1"
            :loading="loading"
          >
            <template #prefix>
              <DatabaseOutlined style="color: #52c41a" />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :xs="24" :sm="12" :md="6">
        <a-card>
          <a-statistic
            title="网络吞吐量"
            :value="performanceStats.avgThroughput"
            suffix="Mbps"
            :precision="2"
            :loading="loading"
          >
            <template #prefix>
              <GlobalOutlined style="color: #fa8c16" />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :xs="24" :sm="12" :md="6">
        <a-card>
          <a-statistic
            title="响应时间"
            :value="performanceStats.avgResponseTime"
            suffix="ms"
            :precision="0"
            :loading="loading"
          >
            <template #prefix>
              <ClockCircleOutlined style="color: #722ed1" />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
    </a-row>

    <!-- 性能图表 -->
    <a-row :gutter="16" class="charts-row">
      <!-- CPU使用率图表 -->
      <a-col :xs="24" :lg="12" v-if="filters.metricTypes.includes('cpu')">
        <a-card title="CPU使用率趋势" class="chart-card">
          <template #extra>
            <a-space>
              <a-tag :color="getCpuStatusColor(performanceStats.currentCpu)">
                当前: {{ performanceStats.currentCpu }}%
              </a-tag>
            </a-space>
          </template>
          <v-chart
            ref="cpuChart"
            :option="cpuChartOption"
            :loading="loading"
            style="height: 300px"
            autoresize
          />
        </a-card>
      </a-col>

      <!-- 内存使用率图表 -->
      <a-col :xs="24" :lg="12" v-if="filters.metricTypes.includes('memory')">
        <a-card title="内存使用率趋势" class="chart-card">
          <template #extra>
            <a-space>
              <a-tag :color="getMemoryStatusColor(performanceStats.currentMemory)">
                当前: {{ performanceStats.currentMemory }}%
              </a-tag>
            </a-space>
          </template>
          <v-chart
            ref="memoryChart"
            :option="memoryChartOption"
            :loading="loading"
            style="height: 300px"
            autoresize
          />
        </a-card>
      </a-col>

      <!-- 网络流量图表 -->
      <a-col :xs="24" :lg="12" v-if="filters.metricTypes.includes('network')">
        <a-card title="网络流量趋势" class="chart-card">
          <template #extra>
            <a-space>
              <a-tag color="blue">
                入流量: {{ formatBytes(performanceStats.currentInbound) }}/s
              </a-tag>
              <a-tag color="green">
                出流量: {{ formatBytes(performanceStats.currentOutbound) }}/s
              </a-tag>
            </a-space>
          </template>
          <v-chart
            ref="networkChart"
            :option="networkChartOption"
            :loading="loading"
            style="height: 300px"
            autoresize
          />
        </a-card>
      </a-col>

      <!-- 磁盘使用率图表 -->
      <a-col :xs="24" :lg="12" v-if="filters.metricTypes.includes('disk')">
        <a-card title="磁盘使用率趋势" class="chart-card">
          <template #extra>
            <a-space>
              <a-tag :color="getDiskStatusColor(performanceStats.currentDisk)">
                当前: {{ performanceStats.currentDisk }}%
              </a-tag>
            </a-space>
          </template>
          <v-chart
            ref="diskChart"
            :option="diskChartOption"
            :loading="loading"
            style="height: 300px"
            autoresize
          />
        </a-card>
      </a-col>
    </a-row>

    <!-- 性能告警 -->
    <a-card title="性能告警" class="alerts-card" v-if="performanceAlerts.length > 0">
      <a-list
        :data-source="performanceAlerts"
        :pagination="{ pageSize: 5 }"
      >
        <template #renderItem="{ item }">
          <a-list-item>
            <a-list-item-meta>
              <template #avatar>
                <a-avatar :style="{ backgroundColor: getAlertColor(item.level) }">
                  <component :is="getAlertIcon(item.level)" />
                </a-avatar>
              </template>
              <template #title>
                <span>{{ item.title }}</span>
                <a-tag :color="getAlertColor(item.level)" style="margin-left: 8px">
                  {{ item.level }}
                </a-tag>
              </template>
              <template #description>
                {{ item.description }}
              </template>
            </a-list-item-meta>
            <template #actions>
              <span>{{ formatTimestamp(item.timestamp) }}</span>
            </template>
          </a-list-item>
        </template>
      </a-list>
    </a-card>

    <!-- 性能详情表格 -->
    <a-card title="性能详情数据" class="details-card">
      <template #extra>
        <a-space>
          <span>数据点: {{ performanceData.length }}</span>
          <a-button size="small" @click="showRawData = !showRawData">
            {{ showRawData ? '隐藏' : '显示' }}原始数据
          </a-button>
        </a-space>
      </template>

      <a-table
        v-if="showRawData"
        :columns="performanceColumns"
        :data-source="performanceData.slice(0, 100)"
        :pagination="{ pageSize: 10 }"
        size="small"
        :scroll="{ x: 800 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'timestamp'">
            {{ formatTimestamp(record.timestamp) }}
          </template>
          <template v-else-if="column.key === 'cpu_usage'">
            <a-progress
              :percent="record.cpu_usage"
              size="small"
              :stroke-color="getCpuStatusColor(record.cpu_usage)"
            />
          </template>
          <template v-else-if="column.key === 'memory_usage'">
            <a-progress
              :percent="record.memory_usage"
              size="small"
              :stroke-color="getMemoryStatusColor(record.memory_usage)"
            />
          </template>
          <template v-else-if="column.key === 'network_in'">
            {{ formatBytes(record.network_in) }}/s
          </template>
          <template v-else-if="column.key === 'network_out'">
            {{ formatBytes(record.network_out) }}/s
          </template>
          <template v-else-if="column.key === 'disk_usage'">
            <a-progress
              :percent="record.disk_usage"
              size="small"
              :stroke-color="getDiskStatusColor(record.disk_usage)"
            />
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 详细指标模态框 -->
    <a-modal
      v-model:open="detailedMetricsVisible"
      title="详细性能指标"
      width="1200px"
      :footer="null"
      @cancel="closeDetailedMetrics"
    >
      <div class="detailed-metrics">
        <!-- 指标筛选 -->
        <a-card size="small" class="metrics-filter">
          <a-form layout="inline" :model="metricsFilter">
            <a-form-item label="设备">
              <a-select
                v-model:value="metricsFilter.device_id"
                placeholder="选择设备"
                style="width: 200px"
                allowClear
                @change="loadDetailedMetrics"
              >
                <a-select-option
                  v-for="device in devices"
                  :key="device.id"
                  :value="device.id"
                >
                  {{ device.name }}
                </a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="指标类型">
              <a-select
                v-model:value="metricsFilter.metric_name"
                placeholder="选择指标类型"
                style="width: 200px"
                allowClear
                @change="loadDetailedMetrics"
              >
                <a-select-option value="cpu_usage">CPU使用率</a-select-option>
                <a-select-option value="memory_usage">内存使用率</a-select-option>
                <a-select-option value="disk_usage">磁盘使用率</a-select-option>
                <a-select-option value="network_traffic">网络流量</a-select-option>
                <a-select-option value="response_time">响应时间</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="时间范围">
              <a-select
                v-model:value="metricsFilter.hours"
                style="width: 150px"
                @change="loadDetailedMetrics"
              >
                <a-select-option :value="1">最近1小时</a-select-option>
                <a-select-option :value="6">最近6小时</a-select-option>
                <a-select-option :value="24">最近24小时</a-select-option>
                <a-select-option :value="72">最近3天</a-select-option>
                <a-select-option :value="168">最近7天</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item>
              <a-button @click="loadDetailedMetrics" :loading="metricsLoading">
                <template #icon><SearchOutlined /></template>
                查询
              </a-button>
            </a-form-item>
          </a-form>
        </a-card>

        <!-- 指标数据表格 -->
        <a-card size="small" style="margin-top: 16px;">
          <template #title>
            <a-space>
              <span>指标数据</span>
              <a-tag color="blue">{{ detailedMetrics.length }} 条记录</a-tag>
            </a-space>
          </template>
          
          <template #extra>
            <a-button @click="exportDetailedMetrics" size="small">
              <template #icon><DownloadOutlined /></template>
              导出
            </a-button>
          </template>

          <a-table
            :columns="metricsColumns"
            :data-source="detailedMetrics"
            :loading="metricsLoading"
            :pagination="{
              total: detailedMetrics.length,
              pageSize: 20,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 条记录`
            }"
            size="small"
            :scroll="{ x: 800 }"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'metric_value'">
                <a-tag
                  :color="getMetricValueColor(record.metric_name, record.metric_value)"
                >
                  {{ formatMetricValue(record.metric_name, record.metric_value) }}
                </a-tag>
              </template>
              <template v-else-if="column.key === 'timestamp'">
                {{ formatTimestamp(record.timestamp) }}
              </template>
            </template>
          </a-table>
        </a-card>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import { use } from 'echarts/core'
import { LineChart, BarChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent
} from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'
import VChart from 'vue-echarts'
import {
  ReloadOutlined,
  DownloadOutlined,
  ProcessorOutlined,
  DatabaseOutlined,
  GlobalOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  WarningOutlined,
  InfoCircleOutlined,
  LineChartOutlined,
  SearchOutlined
} from '@ant-design/icons-vue'

import { performanceApi, type PerformanceMetric, type PerformanceDataParams } from '@/api/performance'
import { deviceApi, type Device } from '@/api/device'

// 注册ECharts组件
use([
  LineChart,
  BarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent,
  CanvasRenderer
])

// 类型定义
interface PerformanceAlert {
  id: string
  title: string
  description: string
  level: 'info' | 'warning' | 'error'
  timestamp: string
}

interface PerformanceStats {
  avgCpu: number
  avgMemory: number
  avgThroughput: number
  avgResponseTime: number
  currentCpu: number
  currentMemory: number
  currentDisk: number
  currentInbound: number
  currentOutbound: number
}

// 响应式数据
const loading = ref(false)
const autoRefresh = ref(false)
const showRawData = ref(false)
const devices = ref<Device[]>([])
const performanceData = ref<PerformanceMetric[]>([])
const performanceAlerts = ref<PerformanceAlert[]>([])

// 详细指标状态
const detailedMetricsVisible = ref(false)
const metricsLoading = ref(false)
const detailedMetrics = ref<any[]>([])
const metricsFilter = ref({
  device_id: undefined as number | undefined,
  metric_name: undefined as string | undefined,
  hours: 24
})

// 筛选器
const filters = ref({
  deviceId: undefined as number | undefined,
  timeRange: '24h',
  metricTypes: ['cpu', 'memory', 'network', 'disk']
})

// 性能统计
const performanceStats = ref<PerformanceStats>({
  avgCpu: 0,
  avgMemory: 0,
  avgThroughput: 0,
  avgResponseTime: 0,
  currentCpu: 0,
  currentMemory: 0,
  currentDisk: 0,
  currentInbound: 0,
  currentOutbound: 0
})

// 自动刷新定时器
let refreshTimer: NodeJS.Timeout | null = null

// 表格列定义
const performanceColumns = [
  { title: '时间', key: 'timestamp', width: 150 },
  { title: 'CPU使用率', key: 'cpu_usage', width: 120 },
  { title: '内存使用率', key: 'memory_usage', width: 120 },
  { title: '网络入流量', key: 'network_in', width: 120 },
  { title: '网络出流量', key: 'network_out', width: 120 },
  { title: '磁盘使用率', key: 'disk_usage', width: 120 },
  { title: '响应时间', dataIndex: 'response_time', key: 'response_time', width: 100 }
]

// 详细指标表格列定义
const metricsColumns = [
  { 
    title: '设备ID', 
    dataIndex: 'device_id', 
    key: 'device_id', 
    width: 100,
    sorter: (a: any, b: any) => a.device_id - b.device_id
  },
  { 
    title: '指标名称', 
    dataIndex: 'metric_name', 
    key: 'metric_name', 
    width: 120,
    filters: [
      { text: 'CPU使用率', value: 'cpu_usage' },
      { text: '内存使用率', value: 'memory_usage' },
      { text: '磁盘使用率', value: 'disk_usage' },
      { text: '网络流量', value: 'network_traffic' },
      { text: '响应时间', value: 'response_time' }
    ],
    onFilter: (value: string, record: any) => record.metric_name === value
  },
  { 
    title: '指标值', 
    dataIndex: 'metric_value', 
    key: 'metric_value', 
    width: 120,
    sorter: (a: any, b: any) => a.metric_value - b.metric_value
  },
  { 
    title: '单位', 
    dataIndex: 'unit', 
    key: 'unit', 
    width: 80 
  },
  { 
    title: '时间戳', 
    dataIndex: 'timestamp', 
    key: 'timestamp', 
    width: 180,
    sorter: (a: any, b: any) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
  },
  { 
    title: '状态', 
    dataIndex: 'status', 
    key: 'status', 
    width: 100 
  }
]

// 图表配置
const cpuChartOption = computed(() => ({
  title: {
    text: 'CPU使用率 (%)',
    left: 'center',
    textStyle: { fontSize: 14 }
  },
  tooltip: {
    trigger: 'axis',
    formatter: (params: any) => {
      const data = params[0]
      return `${data.name}<br/>CPU使用率: ${data.value}%`
    }
  },
  xAxis: {
    type: 'category',
    data: performanceData.value.map(item =>
      dayjs(item.timestamp).format('HH:mm')
    )
  },
  yAxis: {
    type: 'value',
    min: 0,
    max: 100,
    axisLabel: {
      formatter: '{value}%'
    }
  },
  series: [{
    name: 'CPU使用率',
    type: 'line',
    data: performanceData.value.map(item => item.cpu_usage || 0),
    smooth: true,
    lineStyle: { color: '#1890ff' },
    areaStyle: { color: 'rgba(24, 144, 255, 0.1)' }
  }],
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  }
}))

const memoryChartOption = computed(() => ({
  title: {
    text: '内存使用率 (%)',
    left: 'center',
    textStyle: { fontSize: 14 }
  },
  tooltip: {
    trigger: 'axis',
    formatter: (params: any) => {
      const data = params[0]
      return `${data.name}<br/>内存使用率: ${data.value}%`
    }
  },
  xAxis: {
    type: 'category',
    data: performanceData.value.map(item =>
      dayjs(item.timestamp).format('HH:mm')
    )
  },
  yAxis: {
    type: 'value',
    min: 0,
    max: 100,
    axisLabel: {
      formatter: '{value}%'
    }
  },
  series: [{
    name: '内存使用率',
    type: 'line',
    data: performanceData.value.map(item => item.memory_usage || 0),
    smooth: true,
    lineStyle: { color: '#52c41a' },
    areaStyle: { color: 'rgba(82, 196, 26, 0.1)' }
  }],
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  }
}))

const networkChartOption = computed(() => ({
  title: {
    text: '网络流量 (Mbps)',
    left: 'center',
    textStyle: { fontSize: 14 }
  },
  tooltip: {
    trigger: 'axis',
    formatter: (params: any) => {
      const inbound = params[0]
      const outbound = params[1]
      return `${inbound.name}<br/>入流量: ${formatBytes(inbound.value)}/s<br/>出流量: ${formatBytes(outbound.value)}/s`
    }
  },
  legend: {
    data: ['入流量', '出流量'],
    bottom: 0
  },
  xAxis: {
    type: 'category',
    data: performanceData.value.map(item =>
      dayjs(item.timestamp).format('HH:mm')
    )
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: (value: number) => formatBytes(value) + '/s'
    }
  },
  series: [
    {
      name: '入流量',
      type: 'line',
      data: performanceData.value.map(item => item.network_in || 0),
      smooth: true,
      lineStyle: { color: '#1890ff' }
    },
    {
      name: '出流量',
      type: 'line',
      data: performanceData.value.map(item => item.network_out || 0),
      smooth: true,
      lineStyle: { color: '#52c41a' }
    }
  ],
  grid: {
    left: '3%',
    right: '4%',
    bottom: '15%',
    containLabel: true
  }
}))

const diskChartOption = computed(() => ({
  title: {
    text: '磁盘使用率 (%)',
    left: 'center',
    textStyle: { fontSize: 14 }
  },
  tooltip: {
    trigger: 'axis',
    formatter: (params: any) => {
      const data = params[0]
      return `${data.name}<br/>磁盘使用率: ${data.value}%`
    }
  },
  xAxis: {
    type: 'category',
    data: performanceData.value.map(item =>
      dayjs(item.timestamp).format('HH:mm')
    )
  },
  yAxis: {
    type: 'value',
    min: 0,
    max: 100,
    axisLabel: {
      formatter: '{value}%'
    }
  },
  series: [{
    name: '磁盘使用率',
    type: 'line',
    data: performanceData.value.map(item => item.disk_usage || 0),
    smooth: true,
    lineStyle: { color: '#fa8c16' },
    areaStyle: { color: 'rgba(250, 140, 22, 0.1)' }
  }],
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  }
}))

// 方法
const loadDevices = async () => {
  try {
    const response = await deviceApi.getDevices()
    devices.value = response.data || []
  } catch (error: any) {
    console.error('加载设备列表失败:', error)
  }
}

const loadPerformanceData = async () => {
  if (!filters.value.deviceId) {
    performanceData.value = []
    return
  }

  loading.value = true
  try {
    const params = {
      device_id: filters.value.deviceId,
      time_range: filters.value.timeRange,
      metric_types: filters.value.metricTypes.join(',')
    }

    const response = await performanceApi.getPerformanceData(params)
    performanceData.value = response.data || []

    // 计算性能统计
    calculatePerformanceStats()

    // 检查性能告警
    checkPerformanceAlerts()

  } catch (error: any) {
    message.error(`加载性能数据失败: ${error.message}`)
  } finally {
    loading.value = false
  }
}

const calculatePerformanceStats = () => {
  if (performanceData.value.length === 0) {
    performanceStats.value = {
      avgCpu: 0,
      avgMemory: 0,
      avgThroughput: 0,
      avgResponseTime: 0,
      currentCpu: 0,
      currentMemory: 0,
      currentDisk: 0,
      currentInbound: 0,
      currentOutbound: 0
    }
    return
  }

  const data = performanceData.value
  const latest = data[data.length - 1]

  // 计算平均值
  const avgCpu = data.reduce((sum, item) => sum + (item.cpu_usage || 0), 0) / data.length
  const avgMemory = data.reduce((sum, item) => sum + (item.memory_usage || 0), 0) / data.length
  const avgThroughput = data.reduce((sum, item) => sum + ((item.network_in || 0) + (item.network_out || 0)), 0) / data.length / 1024 / 1024 // 转换为Mbps
  const avgResponseTime = data.reduce((sum, item) => sum + (item.response_time || 0), 0) / data.length

  performanceStats.value = {
    avgCpu: Number(avgCpu.toFixed(1)),
    avgMemory: Number(avgMemory.toFixed(1)),
    avgThroughput: Number(avgThroughput.toFixed(2)),
    avgResponseTime: Number(avgResponseTime.toFixed(0)),
    currentCpu: latest.cpu_usage || 0,
    currentMemory: latest.memory_usage || 0,
    currentDisk: latest.disk_usage || 0,
    currentInbound: latest.network_in || 0,
    currentOutbound: latest.network_out || 0
  }
}

const checkPerformanceAlerts = () => {
  const alerts: PerformanceAlert[] = []
  const latest = performanceData.value[performanceData.value.length - 1]

  if (!latest) return

  // CPU告警
  if (latest.cpu_usage && latest.cpu_usage > 90) {
    alerts.push({
      id: `cpu_${Date.now()}`,
      title: 'CPU使用率过高',
      description: `当前CPU使用率为 ${latest.cpu_usage}%，超过90%阈值`,
      level: 'error',
      timestamp: latest.timestamp
    })
  } else if (latest.cpu_usage && latest.cpu_usage > 80) {
    alerts.push({
      id: `cpu_${Date.now()}`,
      title: 'CPU使用率较高',
      description: `当前CPU使用率为 ${latest.cpu_usage}%，超过80%阈值`,
      level: 'warning',
      timestamp: latest.timestamp
    })
  }

  // 内存告警
  if (latest.memory_usage && latest.memory_usage > 90) {
    alerts.push({
      id: `memory_${Date.now()}`,
      title: '内存使用率过高',
      description: `当前内存使用率为 ${latest.memory_usage}%，超过90%阈值`,
      level: 'error',
      timestamp: latest.timestamp
    })
  } else if (latest.memory_usage && latest.memory_usage > 80) {
    alerts.push({
      id: `memory_${Date.now()}`,
      title: '内存使用率较高',
      description: `当前内存使用率为 ${latest.memory_usage}%，超过80%阈值`,
      level: 'warning',
      timestamp: latest.timestamp
    })
  }

  // 磁盘告警
  if (latest.disk_usage && latest.disk_usage > 95) {
    alerts.push({
      id: `disk_${Date.now()}`,
      title: '磁盘空间不足',
      description: `当前磁盘使用率为 ${latest.disk_usage}%，超过95%阈值`,
      level: 'error',
      timestamp: latest.timestamp
    })
  } else if (latest.disk_usage && latest.disk_usage > 85) {
    alerts.push({
      id: `disk_${Date.now()}`,
      title: '磁盘空间较少',
      description: `当前磁盘使用率为 ${latest.disk_usage}%，超过85%阈值`,
      level: 'warning',
      timestamp: latest.timestamp
    })
  }

  performanceAlerts.value = alerts
}

const refreshData = () => {
  loadPerformanceData()
}

const exportData = () => {
  if (performanceData.value.length === 0) {
    message.warning('没有可导出的数据')
    return
  }

  const csvContent = [
    ['时间', 'CPU使用率(%)', '内存使用率(%)', '网络入流量(bytes/s)', '网络出流量(bytes/s)', '磁盘使用率(%)', '响应时间(ms)'],
    ...performanceData.value.map(item => [
      formatTimestamp(item.timestamp),
      item.cpu_usage || 0,
      item.memory_usage || 0,
      item.network_in || 0,
      item.network_out || 0,
      item.disk_usage || 0,
      item.response_time || 0
    ])
  ].map(row => row.join(',')).join('\n')

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `performance-data-${dayjs().format('YYYY-MM-DD-HH-mm-ss')}.csv`
  a.click()
  URL.revokeObjectURL(url)
}

// 详细指标相关方法
const showDetailedMetrics = () => {
  detailedMetricsVisible.value = true
  // 设置默认筛选条件
  if (filters.value.deviceId) {
    metricsFilter.value.device_id = filters.value.deviceId
  }
  // 加载详细指标数据
  loadDetailedMetrics()
}

const closeDetailedMetrics = () => {
  detailedMetricsVisible.value = false
  detailedMetrics.value = []
}

const loadDetailedMetrics = async () => {
  metricsLoading.value = true
  try {
    // 参数验证
    if (metricsFilter.value.hours <= 0 || metricsFilter.value.hours > 168) {
      throw new Error('时间范围必须在1-168小时之间')
    }
    
    const params = {
      device_id: metricsFilter.value.device_id,
      metric_name: metricsFilter.value.metric_name,
      hours: metricsFilter.value.hours
    }

    // 添加超时控制
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('请求超时，请稍后重试')), 15000)
    })
    
    const apiPromise = performanceApi.getMetrics(params)
    const response = await Promise.race([apiPromise, timeoutPromise]) as any
    
    // 验证响应数据
    if (response && response.data) {
      if (Array.isArray(response.data)) {
        detailedMetrics.value = response.data
        if (response.data.length === 0) {
          message.info('未找到符合条件的指标数据')
        } else {
          message.success(`成功加载${response.data.length}条详细指标数据`)
        }
      } else {
        throw new Error('响应数据格式错误')
      }
    } else {
      detailedMetrics.value = []
      message.warning('未获取到有效数据')
    }
  } catch (error: any) {
    console.error('加载详细指标失败:', error)
    message.error(`加载详细指标失败: ${error.message}`)
  } finally {
    metricsLoading.value = false
  }
}

const exportDetailedMetrics = () => {
  if (detailedMetrics.value.length === 0) {
    message.warning('没有可导出的详细指标数据')
    return
  }

  const csvContent = [
    ['设备ID', '指标名称', '指标值', '单位', '时间戳', '状态'],
    ...detailedMetrics.value.map(item => [
      item.device_id || '',
      item.metric_name || '',
      item.metric_value || 0,
      item.unit || '',
      formatTimestamp(item.timestamp),
      item.status || ''
    ])
  ].map(row => row.join(',')).join('
')

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `detailed-metrics-${dayjs().format('YYYY-MM-DD-HH-mm-ss')}.csv`
  a.click()
  URL.revokeObjectURL(url)
}

const formatMetricValue = (metricName: string, value: number) => {
  switch (metricName) {
    case 'cpu_usage':
    case 'memory_usage':
    case 'disk_usage':
      return `${value.toFixed(1)}%`
    case 'network_traffic':
      return formatBytes(value)
    case 'response_time':
      return `${value.toFixed(0)}ms`
    default:
      return value.toString()
  }
}

const getMetricValueColor = (metricName: string, value: number) => {
  switch (metricName) {
    case 'cpu_usage':
    case 'memory_usage':
    case 'disk_usage':
      if (value >= 90) return 'red'
      if (value >= 70) return 'orange'
      if (value >= 50) return 'yellow'
      return 'green'
    case 'response_time':
      if (value >= 1000) return 'red'
      if (value >= 500) return 'orange'
      if (value >= 200) return 'yellow'
      return 'green'
    default:
      return 'blue'
  }
}

const formatTimestamp = (timestamp: string) => {
  return dayjs(timestamp).format('YYYY-MM-DD HH:mm:ss')
}

const toggleAutoRefresh = (enabled: boolean) => {
  if (enabled) {
    refreshTimer = setInterval(() => {
      loadPerformanceData()
    }, 30000) // 30秒刷新一次
  } else {
    if (refreshTimer) {
      clearInterval(refreshTimer)
      refreshTimer = null
    }
  }
}

// 工具函数
const formatBytes = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatTimestamp = (timestamp: string): string => {
  return dayjs(timestamp).format('YYYY-MM-DD HH:mm:ss')
}

const getCpuStatusColor = (usage: number): string => {
  if (usage >= 90) return '#ff4d4f'
  if (usage >= 80) return '#fa8c16'
  if (usage >= 60) return '#faad14'
  return '#52c41a'
}

const getMemoryStatusColor = (usage: number): string => {
  if (usage >= 90) return '#ff4d4f'
  if (usage >= 80) return '#fa8c16'
  if (usage >= 60) return '#faad14'
  return '#52c41a'
}

const getDiskStatusColor = (usage: number): string => {
  if (usage >= 95) return '#ff4d4f'
  if (usage >= 85) return '#fa8c16'
  if (usage >= 70) return '#faad14'
  return '#52c41a'
}

const getAlertColor = (level: string): string => {
  const colors = {
    error: '#ff4d4f',
    warning: '#fa8c16',
    info: '#1890ff'
  }
  return colors[level as keyof typeof colors] || '#1890ff'
}

const getAlertIcon = (level: string) => {
  const icons = {
    error: ExclamationCircleOutlined,
    warning: WarningOutlined,
    info: InfoCircleOutlined
  }
  return icons[level as keyof typeof icons] || InfoCircleOutlined
}

const filterDeviceOption = (input: string, option: any) => {
  return option.children.toLowerCase().includes(input.toLowerCase())
}

// 生命周期
onMounted(() => {
  loadDevices()
  if (filters.value.deviceId) {
    loadPerformanceData()
  }
})

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
})
</script>

<style scoped>
.performance-monitoring {
  padding: 24px;
}

.filter-card {
  margin-bottom: 16px;
}

.stats-row {
  margin-bottom: 16px;
}

.charts-row {
  margin-bottom: 16px;
}

.chart-card {
  margin-bottom: 16px;
}

.alerts-card {
  margin-bottom: 16px;
}

.details-card {
  margin-bottom: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .performance-monitoring {
    padding: 16px;
  }

  .stats-row .ant-col {
    margin-bottom: 16px;
  }

  .charts-row .ant-col {
    margin-bottom: 16px;
  }
}

/* 图表容器样式 */
.chart-card .ant-card-body {
  padding: 16px;
}

/* 告警列表样式 */
.alerts-card .ant-list-item {
  padding: 12px 16px;
}

.alerts-card .ant-list-item-meta-title {
  margin-bottom: 4px;
}

.alerts-card .ant-list-item-meta-description {
  color: #666;
  font-size: 12px;
}

/* 性能统计卡片样式 */
.stats-row .ant-statistic {
  text-align: center;
}

.stats-row .ant-statistic-title {
  font-size: 14px;
  color: #666;
}

.stats-row .ant-statistic-content {
  font-size: 24px;
  font-weight: 600;
}

/* 筛选器样式 */
.filter-card .ant-form-item {
  margin-bottom: 16px;
}

.filter-card .ant-checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.filter-card .ant-checkbox-wrapper {
  margin-right: 0;
}

/* 详细指标模态框样式 */
.detailed-metrics {
  .metrics-filter {
    margin-bottom: 16px;
    
    .ant-form-item {
      margin-bottom: 8px;
    }
  }
  
  .ant-table-wrapper {
    .ant-table-thead > tr > th {
      background-color: #fafafa;
      font-weight: 600;
    }
    
    .ant-table-tbody > tr:hover > td {
      background-color: #f5f5f5;
    }
  }
}
</style>