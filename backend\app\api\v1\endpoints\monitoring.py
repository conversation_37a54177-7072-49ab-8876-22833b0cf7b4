"""
Monitoring management endpoints
"""
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta
from fastapi import APIRouter, HTTPException, Query, Depends
from sqlalchemy.orm import Session
from pydantic import BaseModel
from loguru import logger

from ....services.monitoring_service import monitoring_service
from ....core.database import get_db
from ....models.device import Device
from ....models.performance import PerformanceMetric
from ....models.alert import Alert

router = APIRouter()

# Pydantic models for request/response
class MonitoringConfig(BaseModel):
    collection_interval: int = 60
    snmp_timeout: int = 5
    snmp_retries: int = 3
    max_concurrent_collections: int = 10
    enable_auto_discovery: bool = False
    discovery_interval: int = 3600
    alert_thresholds: Dict[str, int] = {
        "cpu_threshold": 80,
        "memory_threshold": 80,
        "disk_threshold": 90,
        "network_threshold": 80
    }
    retention_policy: Dict[str, int] = {
        "performance_data_days": 30,
        "alert_data_days": 90,
        "log_data_days": 7
    }

class CleanupOptions(BaseModel):
    cleanup_performance: bool = False
    cleanup_alerts: bool = False
    cleanup_logs: bool = False
    days_to_keep: int = 30

class ExportOptions(BaseModel):
    data_type: str  # 'performance', 'alerts', 'devices', 'all'
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    format: str = "json"  # 'json', 'csv', 'excel'

class DeviceDiscoveryRequest(BaseModel):
    ip_range: str
    scan_type: str = "ping"  # 'ping', 'snmp', 'full'
    timeout: int = 5
    concurrent_limit: int = 50


@router.get("/status")
async def get_monitoring_status():
    """Get monitoring service status"""
    try:
        status = await monitoring_service.get_monitoring_status()
        return status
        
    except Exception as e:
        logger.error(f"Error getting monitoring status: {e}")
        raise HTTPException(status_code=500, detail="Failed to get monitoring status")


@router.post("/start")
async def start_monitoring():
    """Start monitoring service"""
    try:
        await monitoring_service.start_monitoring()
        return {"message": "Monitoring service started successfully"}
        
    except Exception as e:
        logger.error(f"Error starting monitoring service: {e}")
        raise HTTPException(status_code=500, detail="Failed to start monitoring service")


@router.post("/stop")
async def stop_monitoring():
    """Stop monitoring service"""
    try:
        await monitoring_service.stop_monitoring()
        return {"message": "Monitoring service stopped successfully"}
        
    except Exception as e:
        logger.error(f"Error stopping monitoring service: {e}")
        raise HTTPException(status_code=500, detail="Failed to stop monitoring service")


@router.post("/discover")
async def discover_devices(ip_range: str = Query(..., description="IP range to scan (e.g., ***********/24)")):
    """Trigger device discovery"""
    try:
        result = await monitoring_service.trigger_device_discovery(ip_range)
        return result
        
    except Exception as e:
        logger.error(f"Error in device discovery: {e}")
        raise HTTPException(status_code=500, detail="Failed to discover devices")


@router.post("/monitor-now")
async def trigger_monitoring():
    """Manually trigger device monitoring cycle"""
    try:
        from ....services.device_service import DeviceService
        
        device_service = DeviceService()
        result = await device_service.monitor_all_devices()
        
        return {
            "message": "Manual monitoring cycle completed",
            "result": result
        }
        
    except Exception as e:
        logger.error(f"Error in manual monitoring: {e}")
        raise HTTPException(status_code=500, detail="Failed to trigger monitoring")


@router.post("/restart")
async def restart_monitoring():
    """Restart monitoring service"""
    try:
        await monitoring_service.stop_monitoring()
        await monitoring_service.start_monitoring()
        return {
            "success": True,
            "message": "Monitoring service restarted successfully",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error restarting monitoring service: {e}")
        raise HTTPException(status_code=500, detail="Failed to restart monitoring service")


@router.get("/discover/{task_id}")
async def get_discovery_status(task_id: str):
    """Get device discovery status"""
    try:
        # Mock implementation - in real scenario, this would check task status
        return {
            "task_id": task_id,
            "status": "completed",
            "progress": 100,
            "discovered_devices": [],
            "total_scanned": 0,
            "scan_start_time": datetime.now().isoformat(),
            "scan_end_time": datetime.now().isoformat(),
            "errors": []
        }
        
    except Exception as e:
        logger.error(f"Error getting discovery status: {e}")
        raise HTTPException(status_code=500, detail="Failed to get discovery status")


@router.get("/config")
async def get_monitoring_config():
    """Get monitoring configuration"""
    try:
        # Return default configuration - in real scenario, this would be stored in database
        config = MonitoringConfig()
        return config.dict()
        
    except Exception as e:
        logger.error(f"Error getting monitoring config: {e}")
        raise HTTPException(status_code=500, detail="Failed to get monitoring configuration")


@router.put("/config")
async def update_monitoring_config(config: MonitoringConfig):
    """Update monitoring configuration"""
    try:
        # Mock implementation - in real scenario, this would update database
        return {
            "success": True,
            "message": "Configuration updated successfully",
            "config": config.dict()
        }
        
    except Exception as e:
        logger.error(f"Error updating monitoring config: {e}")
        raise HTTPException(status_code=500, detail="Failed to update monitoring configuration")


@router.get("/collectors")
async def get_collector_status():
    """Get collector status"""
    try:
        # Mock collector data - in real scenario, this would query actual collectors
        collectors = [
            {
                "collector_id": "snmp_collector_1",
                "collector_type": "snmp",
                "status": "active",
                "assigned_devices": 10,
                "last_collection": datetime.now().isoformat(),
                "collection_rate": 95.5,
                "error_rate": 2.1,
                "performance_metrics": {
                    "avg_response_time": 150,
                    "success_rate": 97.9,
                    "data_points_collected": 1250
                }
            },
            {
                "collector_id": "ping_collector_1",
                "collector_type": "ping",
                "status": "active",
                "assigned_devices": 25,
                "last_collection": datetime.now().isoformat(),
                "collection_rate": 98.2,
                "error_rate": 1.8,
                "performance_metrics": {
                    "avg_response_time": 50,
                    "success_rate": 98.2,
                    "data_points_collected": 2500
                }
            }
        ]
        
        return {
            "collectors": collectors,
            "total_collectors": len(collectors),
            "active_collectors": len([c for c in collectors if c["status"] == "active"]),
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting collector status: {e}")
        raise HTTPException(status_code=500, detail="Failed to get collector status")


@router.post("/collectors/{collector_id}/restart")
async def restart_collector(collector_id: str):
    """Restart specific collector"""
    try:
        # Mock implementation - in real scenario, this would restart the actual collector
        return {
            "success": True,
            "message": f"Collector {collector_id} restarted successfully"
        }
        
    except Exception as e:
        logger.error(f"Error restarting collector {collector_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to restart collector {collector_id}")


@router.get("/statistics")
async def get_monitoring_statistics(
    time_range: Optional[str] = Query("24h", description="Time range for statistics"),
    db: Session = Depends(get_db)
):
    """Get monitoring statistics"""
    try:
        # Parse time range
        end_time = datetime.now()
        if time_range == "1h":
            start_time = end_time - timedelta(hours=1)
        elif time_range == "6h":
            start_time = end_time - timedelta(hours=6)
        elif time_range == "24h":
            start_time = end_time - timedelta(hours=24)
        elif time_range == "7d":
            start_time = end_time - timedelta(days=7)
        elif time_range == "30d":
            start_time = end_time - timedelta(days=30)
        else:
            start_time = end_time - timedelta(hours=24)

        # Get device statistics
        total_devices = db.query(Device).filter(Device.is_active == True).count()
        online_devices = db.query(Device).filter(
            Device.is_active == True,
            Device.status == "online"
        ).count()
        offline_devices = db.query(Device).filter(
            Device.is_active == True,
            Device.status == "offline"
        ).count()
        warning_devices = db.query(Device).filter(
            Device.is_active == True,
            Device.status == "warning"
        ).count()

        # Get performance metrics count
        performance_count = db.query(PerformanceMetric).filter(
            PerformanceMetric.is_active == True,
            PerformanceMetric.timestamp >= start_time.isoformat()
        ).count()

        # Get alert statistics
        alert_count = db.query(Alert).filter(
            Alert.is_active == True,
            Alert.created_at >= start_time
        ).count()

        return {
            "collection_stats": {
                "total_collections": performance_count,
                "successful_collections": int(performance_count * 0.95),  # Mock 95% success rate
                "failed_collections": int(performance_count * 0.05),
                "avg_collection_time": 2.5
            },
            "device_stats": {
                "total_devices": total_devices,
                "online_devices": online_devices,
                "offline_devices": offline_devices,
                "warning_devices": warning_devices
            },
            "performance_stats": {
                "avg_cpu_usage": 45.2,
                "avg_memory_usage": 62.8,
                "peak_collection_rate": 150.0
            },
            "error_stats": {
                "total_errors": int(performance_count * 0.02),  # Mock 2% error rate
                "error_rate": 2.0,
                "common_errors": [
                    {"error": "SNMP timeout", "count": 15},
                    {"error": "Connection refused", "count": 8},
                    {"error": "Authentication failed", "count": 3}
                ]
            },
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting monitoring statistics: {e}")
        raise HTTPException(status_code=500, detail="Failed to get monitoring statistics")


@router.post("/cleanup")
async def cleanup_monitoring_data(
    options: CleanupOptions,
    db: Session = Depends(get_db)
):
    """Cleanup monitoring data"""
    try:
        cleaned_records = 0
        cutoff_date = datetime.now() - timedelta(days=options.days_to_keep)
        
        if options.cleanup_performance:
            # Delete old performance metrics
            deleted_performance = db.query(PerformanceMetric).filter(
                PerformanceMetric.created_at < cutoff_date
            ).delete()
            cleaned_records += deleted_performance
            
        if options.cleanup_alerts:
            # Delete old alerts
            deleted_alerts = db.query(Alert).filter(
                Alert.created_at < cutoff_date,
                Alert.status == "resolved"
            ).delete()
            cleaned_records += deleted_alerts
            
        if options.cleanup_logs:
            # Mock log cleanup
            cleaned_records += 50  # Mock number
            
        db.commit()
        
        return {
            "success": True,
            "message": "Data cleanup completed successfully",
            "cleaned_records": cleaned_records,
            "freed_space": f"{cleaned_records * 0.5:.1f} MB"  # Mock calculation
        }
        
    except Exception as e:
        logger.error(f"Error cleaning up monitoring data: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to cleanup monitoring data")


@router.post("/export")
async def export_monitoring_data(
    options: ExportOptions,
    db: Session = Depends(get_db)
):
    """Export monitoring data"""
    try:
        # Mock implementation - in real scenario, this would generate actual export files
        file_size = 1024 * 1024  # Mock 1MB file
        
        # Generate mock download URL
        download_url = f"/api/v1/monitoring/download/{datetime.now().strftime('%Y%m%d_%H%M%S')}_{options.data_type}.{options.format}"
        
        return {
            "download_url": download_url,
            "file_size": file_size,
            "expires_at": (datetime.now() + timedelta(hours=24)).isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error exporting monitoring data: {e}")
        raise HTTPException(status_code=500, detail="Failed to export monitoring data")


@router.post("/test-connection/{device_id}")
async def test_device_connection(
    device_id: int,
    db: Session = Depends(get_db)
):
    """Test device connection"""
    try:
        # Get device
        device = db.query(Device).filter(
            Device.id == device_id,
            Device.is_active == True
        ).first()
        
        if not device:
            raise HTTPException(status_code=404, detail="Device not found")
        
        # Mock connection test - in real scenario, this would perform actual ping/SNMP test
        import random
        success = random.choice([True, True, True, False])  # 75% success rate
        response_time = random.uniform(10, 200) if success else 0
        
        return {
            "success": success,
            "response_time": response_time,
            "status": "reachable" if success else "unreachable",
            "details": {
                "device_id": device.id,
                "device_name": device.name,
                "ip_address": device.ip_address,
                "test_type": "ping",
                "timestamp": datetime.now().isoformat()
            },
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error testing device connection: {e}")
        raise HTTPException(status_code=500, detail="Failed to test device connection")
