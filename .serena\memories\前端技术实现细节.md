# CampusGuard前端技术实现细节

## 技术栈配置
- **Vue.js 3.5.17**：使用Composition API
- **构建工具**：Vite 7.0.0
- **UI组件库**：
  - ant-design-vue 4.2.6：主要UI组件
  - ant-design-x-vue 1.2.7：AI对话组件（Conversations）
  - @antv/g6 5.0.49：网络拓扑图
  - echarts 5.6.0：数据可视化图表
  - vue-echarts 7.0.3：Vue的ECharts包装器

## 项目结构
```
frontend/
├── src/
│   ├── api/          # API请求封装
│   ├── components/   # 通用组件
│   ├── layout/       # 布局组件
│   ├── router/       # 路由配置
│   ├── stores/       # Pinia状态管理
│   ├── styles/       # 全局样式
│   ├── types/        # TypeScript类型定义
│   ├── views/        # 页面组件
│   ├── App.vue       # 根组件
│   └── main.ts       # 入口文件
```

## 核心功能实现

### 1. API请求封装（request.ts）
- 基于axios的统一请求拦截
- 自动错误处理和提示
- 请求/响应拦截器
- API基础URL配置

### 2. WebSocket实时通信
- 位置：stores/websocket.ts
- 功能：
  - 自动重连机制
  - 心跳检测
  - 消息分发
  - 连接状态管理
- 支持的消息类型：
  - device_update：设备状态更新
  - alert：告警通知
  - performance：性能数据
  - ai_response：AI回复

### 3. AI对话界面（chat.vue）
- 使用ant-design-x-vue的Conversations组件
- 支持多Agent选择
- 实时流式响应
- 对话历史管理
- Markdown渲染支持

### 4. 网络拓扑图（topology/index.vue）
- 基于@antv/g6实现
- 功能特性：
  - 力导向布局
  - 节点状态可视化（在线/离线）
  - 交互式操作（缩放、拖拽）
  - 设备详情弹窗
  - 实时状态更新

### 5. 监控仪表板（dashboard/index.vue）
- ECharts图表集成
- 实时数据更新
- 响应式布局
- 关键指标卡片

## 状态管理（Pinia）
### stores/app.ts
- 应用全局状态
- 主题设置
- 加载状态

### stores/device.ts
- 设备列表
- 设备状态缓存
- CRUD操作

### stores/alert.ts
- 告警列表
- 告警统计
- 实时告警推送

### stores/websocket.ts
- WebSocket连接管理
- 消息订阅发布

## 路由设计
- 使用Vue Router 4
- 路由懒加载优化
- 布局嵌套结构
- 导航守卫（简化版，无认证）

## 环境配置
```env
# .env.development
VITE_API_BASE_URL=http://localhost:8000/api/v1
VITE_WS_URL=ws://localhost:8000/ws

# .env.production
VITE_API_BASE_URL=/api/v1
VITE_WS_URL=ws://campusguard.example.com/ws
```

## 性能优化
- 组件懒加载
- 图片懒加载
- 虚拟滚动（大数据列表）
- 防抖节流处理
- WebSocket消息批处理

## 响应式设计
- 移动端适配
- 自适应布局
- 触摸手势支持（拓扑图）