"""
Custom exceptions for CampusGuard application
"""
from typing import Any, Optional
from fastapi import HTTPException, status


class AppException(HTTPException):
    """应用级异常基类"""
    def __init__(
        self,
        status_code: int,
        detail: Any = None,
        headers: Optional[dict] = None,
    ):
        super().__init__(status_code=status_code, detail=detail, headers=headers)


class DatabaseException(AppException):
    """数据库相关异常"""
    def __init__(self, detail: str = "Database operation failed"):
        super().__init__(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=detail
        )


class DeviceNotFoundException(AppException):
    """设备未找到异常"""
    def __init__(self, device_id: int = None, ip_address: str = None):
        if device_id:
            detail = f"Device with ID {device_id} not found"
        elif ip_address:
            detail = f"Device with IP {ip_address} not found"
        else:
            detail = "Device not found"
        
        super().__init__(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=detail
        )


class AlertNotFoundException(AppException):
    """告警未找到异常"""
    def __init__(self, alert_id: int):
        super().__init__(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Alert with ID {alert_id} not found"
        )


class AgentException(AppException):
    """AI Agent相关异常"""
    def __init__(self, detail: str = "AI Agent operation failed"):
        super().__init__(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=detail
        )


class ValidationException(AppException):
    """数据验证异常"""
    def __init__(self, detail: str = "Validation failed"):
        super().__init__(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=detail
        )


class MonitoringException(AppException):
    """监控服务异常"""
    def __init__(self, detail: str = "Monitoring operation failed"):
        super().__init__(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=detail
        )


class WebSocketException(AppException):
    """WebSocket相关异常"""
    def __init__(self, detail: str = "WebSocket operation failed"):
        super().__init__(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=detail
        )


class ThreatIntelligenceException(AppException):
    """威胁情报相关异常"""
    def __init__(self, detail: str = "Threat intelligence operation failed"):
        super().__init__(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=detail
        )