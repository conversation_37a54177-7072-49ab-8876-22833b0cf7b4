import type { WebSocketMessage } from '@/stores/websocket'

export interface WebSocketConfig {
  url?: string
  reconnect?: boolean
  reconnectInterval?: number
  reconnectAttempts?: number
  heartbeatInterval?: number
}

export interface WebSocketAPI {
  connect: (userId?: string) => void
  disconnect: () => void
  send: (message: any) => boolean
  subscribe: (event: string, callback: (data: any) => void) => void
  unsubscribe: (event: string, callback?: (data: any) => void) => void
  on: (event: string, callback: (data: any) => void) => void
  off: (event: string, callback?: (data: any) => void) => void
  getStatus: () => 'connecting' | 'connected' | 'disconnected' | 'error'
  isConnected: () => boolean
}

class WebSocketService implements WebSocketAPI {
  private ws: WebSocket | null = null
  private config: Required<WebSocketConfig>
  private listeners: Map<string, Set<(data: any) => void>> = new Map()
  private reconnectTimer: NodeJS.Timeout | null = null
  private heartbeatTimer: NodeJS.Timeout | null = null
  private reconnectCount = 0
  private userId?: string
  private messageQueue: any[] = []
  private isConnecting = false

  constructor(config: WebSocketConfig = {}) {
    this.config = {
      url: config.url || import.meta.env.VITE_WS_URL || 'ws://localhost:8000/api/v1/ws',
      reconnect: config.reconnect !== false,
      reconnectInterval: config.reconnectInterval || 3000,
      reconnectAttempts: config.reconnectAttempts || 5,
      heartbeatInterval: config.heartbeatInterval || 30000
    }
  }

  connect(userId?: string): void {
    if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {
      console.log('WebSocket already connected or connecting')
      return
    }

    this.isConnecting = true
    this.userId = userId

    const url = userId 
      ? `${this.config.url}/connect?user_id=${userId}` 
      : `${this.config.url}/connect`

    try {
      this.ws = new WebSocket(url)
      this.setupEventHandlers()
    } catch (error) {
      console.error('Failed to create WebSocket:', error)
      this.isConnecting = false
      this.handleError(error)
    }
  }

  disconnect(): void {
    this.clearTimers()
    
    if (this.ws) {
      this.ws.close(1000, 'User disconnected')
      this.ws = null
    }
    
    this.isConnecting = false
    this.reconnectCount = 0
    this.messageQueue = []
  }

  send(message: any): boolean {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      // Queue message if configured to do so
      this.messageQueue.push(message)
      return false
    }

    try {
      this.ws.send(JSON.stringify(message))
      return true
    } catch (error) {
      console.error('Failed to send WebSocket message:', error)
      return false
    }
  }

  subscribe(event: string, callback: (data: any) => void): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set())
    }
    this.listeners.get(event)!.add(callback)
    
    // Send subscription message to server
    this.send({
      type: 'subscribe',
      subscription: event
    })
  }

  unsubscribe(event: string, callback?: (data: any) => void): void {
    if (!this.listeners.has(event)) return

    if (callback) {
      this.listeners.get(event)!.delete(callback)
    } else {
      this.listeners.delete(event)
    }

    // Send unsubscribe message to server
    this.send({
      type: 'unsubscribe',
      subscription: event
    })
  }

  on(event: string, callback: (data: any) => void): void {
    this.subscribe(event, callback)
  }

  off(event: string, callback?: (data: any) => void): void {
    this.unsubscribe(event, callback)
  }

  getStatus(): 'connecting' | 'connected' | 'disconnected' | 'error' {
    if (!this.ws) return 'disconnected'
    
    switch (this.ws.readyState) {
      case WebSocket.CONNECTING:
        return 'connecting'
      case WebSocket.OPEN:
        return 'connected'
      case WebSocket.CLOSING:
      case WebSocket.CLOSED:
        return 'disconnected'
      default:
        return 'error'
    }
  }

  isConnected(): boolean {
    return this.ws !== null && this.ws.readyState === WebSocket.OPEN
  }

  private setupEventHandlers(): void {
    if (!this.ws) return

    this.ws.onopen = () => {
      console.log('WebSocket connected')
      this.isConnecting = false
      this.reconnectCount = 0
      
      // Start heartbeat
      this.startHeartbeat()
      
      // Process queued messages
      this.processMessageQueue()
      
      // Emit connected event
      this.emit('connected', { timestamp: new Date().toISOString() })
    }

    this.ws.onmessage = (event) => {
      try {
        const message: WebSocketMessage = JSON.parse(event.data)
        this.handleMessage(message)
      } catch (error) {
        console.error('Failed to parse WebSocket message:', error)
      }
    }

    this.ws.onclose = (event) => {
      console.log('WebSocket disconnected:', event.code, event.reason)
      this.isConnecting = false
      
      // Stop heartbeat
      this.stopHeartbeat()
      
      // Emit disconnected event
      this.emit('disconnected', { 
        code: event.code, 
        reason: event.reason,
        timestamp: new Date().toISOString()
      })
      
      // Attempt to reconnect if not a normal closure
      if (event.code !== 1000 && this.config.reconnect) {
        this.scheduleReconnect()
      }
    }

    this.ws.onerror = (event) => {
      console.error('WebSocket error:', event)
      this.isConnecting = false
      this.handleError(event)
    }
  }

  private handleMessage(message: WebSocketMessage): void {
    // Emit raw message event
    this.emit('message', message)
    
    // Emit typed event
    this.emit(message.type, message.payload || message)
    
    // Special handling for certain message types
    switch (message.type) {
      case 'pong':
        // Handle pong response
        this.emit('pong', { timestamp: message.timestamp })
        break
        
      case 'error':
        this.handleError(message.payload)
        break
        
      case 'subscription':
        console.log('Subscription confirmed:', message.payload)
        break
    }
  }

  private emit(event: string, data: any): void {
    const callbacks = this.listeners.get(event)
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error(`Error in WebSocket event handler for '${event}':`, error)
        }
      })
    }
  }

  private handleError(error: any): void {
    this.emit('error', {
      message: error.message || 'WebSocket error',
      timestamp: new Date().toISOString()
    })
  }

  private startHeartbeat(): void {
    this.stopHeartbeat()
    
    this.heartbeatTimer = setInterval(() => {
      if (this.isConnected()) {
        this.send({
          type: 'ping',
          timestamp: new Date().toISOString()
        })
      }
    }, this.config.heartbeatInterval)
  }

  private stopHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }

  private scheduleReconnect(): void {
    if (this.reconnectTimer || this.reconnectCount >= this.config.reconnectAttempts) {
      return
    }

    this.reconnectCount++
    const delay = this.config.reconnectInterval * Math.pow(2, this.reconnectCount - 1)
    
    console.log(`Scheduling reconnect attempt ${this.reconnectCount} in ${delay}ms`)
    
    this.reconnectTimer = setTimeout(() => {
      this.reconnectTimer = null
      this.connect(this.userId)
    }, delay)
  }

  private clearTimers(): void {
    this.stopHeartbeat()
    
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
  }

  private processMessageQueue(): void {
    while (this.messageQueue.length > 0 && this.isConnected()) {
      const message = this.messageQueue.shift()
      this.send(message)
    }
  }
}

// Export singleton instance
export const websocketApi = new WebSocketService()

// Export class for custom instances
export { WebSocketService }

// Convenience methods for common subscriptions
export const wsSubscriptions = {
  // Device subscriptions
  subscribeToDeviceUpdates: (callback: (data: any) => void) => {
    websocketApi.subscribe('device_update', callback)
  },
  
  unsubscribeFromDeviceUpdates: (callback?: (data: any) => void) => {
    websocketApi.unsubscribe('device_update', callback)
  },

  // Alert subscriptions
  subscribeToAlerts: (callback: (data: any) => void) => {
    websocketApi.subscribe('new_alert', callback)
    websocketApi.subscribe('alert', callback)
  },

  unsubscribeFromAlerts: (callback?: (data: any) => void) => {
    websocketApi.unsubscribe('new_alert', callback)
    websocketApi.unsubscribe('alert', callback)
  },

  // Performance subscriptions
  subscribeToPerformance: (callback: (data: any) => void) => {
    websocketApi.subscribe('performance_update', callback)
    websocketApi.subscribe('performance', callback)
  },

  unsubscribeFromPerformance: (callback?: (data: any) => void) => {
    websocketApi.unsubscribe('performance_update', callback)
    websocketApi.unsubscribe('performance', callback)
  },

  // Topology subscriptions
  subscribeToTopology: (callback: (data: any) => void) => {
    websocketApi.subscribe('topology_update', callback)
  },

  unsubscribeFromTopology: (callback?: (data: any) => void) => {
    websocketApi.unsubscribe('topology_update', callback)
  },

  // AI subscriptions
  subscribeToAI: (callback: (data: any) => void) => {
    websocketApi.subscribe('ai_response', callback)
  },

  unsubscribeFromAI: (callback?: (data: any) => void) => {
    websocketApi.unsubscribe('ai_response', callback)
  },

  // System subscriptions
  subscribeToSystem: (callback: (data: any) => void) => {
    websocketApi.subscribe('system_status', callback)
  },

  unsubscribeFromSystem: (callback?: (data: any) => void) => {
    websocketApi.unsubscribe('system_status', callback)
  },

  // Threat subscriptions
  subscribeToThreats: (callback: (data: any) => void) => {
    websocketApi.subscribe('threat', callback)
  },

  unsubscribeFromThreats: (callback?: (data: any) => void) => {
    websocketApi.unsubscribe('threat', callback)
  }
}

// Export types
export type { WebSocketMessage }