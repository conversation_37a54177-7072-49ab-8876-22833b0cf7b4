<template>
  <div class="error-page">
    <a-result
      status="404"
      title="404"
      sub-title="抱歉，您访问的页面不存在"
    >
      <template #extra>
        <a-button type="primary" @click="goHome">
          返回首页
        </a-button>
        <a-button @click="goBack" style="margin-left: 8px">
          返回上一页
        </a-button>
      </template>
    </a-result>
    
    <div class="suggestions">
      <h3>您可能想要访问：</h3>
      <div class="quick-links">
        <router-link to="/dashboard" class="link-item">
          <DashboardOutlined />
          <span>监控大屏</span>
        </router-link>
        <router-link to="/devices" class="link-item">
          <DesktopOutlined />
          <span>设备管理</span>
        </router-link>
        <router-link to="/alerts" class="link-item">
          <BellOutlined />
          <span>告警管理</span>
        </router-link>
        <router-link to="/ai/chat" class="link-item">
          <RobotOutlined />
          <span>AI助手</span>
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import {
  DashboardOutlined,
  DesktopOutlined,
  BellOutlined,
  RobotOutlined
} from '@ant-design/icons-vue'

const router = useRouter()

const goHome = () => {
  router.push('/dashboard')
}

const goBack = () => {
  router.go(-1)
}
</script>

<style scoped lang="less">
.error-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  
  .suggestions {
    margin-top: 48px;
    text-align: center;
    
    h3 {
      font-size: 16px;
      font-weight: 500;
      color: #666;
      margin-bottom: 24px;
    }
    
    .quick-links {
      display: flex;
      gap: 24px;
      justify-content: center;
      flex-wrap: wrap;
      
      .link-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8px;
        padding: 16px 24px;
        border: 1px solid #e8e8e8;
        border-radius: 8px;
        transition: all 0.3s;
        text-decoration: none;
        color: #666;
        
        &:hover {
          border-color: #1890ff;
          color: #1890ff;
          transform: translateY(-2px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .anticon {
          font-size: 24px;
        }
        
        span {
          font-size: 14px;
        }
      }
    }
  }
}
</style>